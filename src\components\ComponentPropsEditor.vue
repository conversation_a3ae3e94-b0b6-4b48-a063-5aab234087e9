<template>
  <div class="props-editor">
    
    <!-- 根据组件类型显示不同的属性编辑器 -->
    <el-form label-width="80px" size="small">
      
      <!-- 按钮组件属性 -->
      <template v-if="component.type === 'button'">
        <el-form-item label="按钮文字">
          <el-input v-model="createPropModel('text', '按钮').value" />
        </el-form-item>
        <el-form-item label="按钮类型">
          <el-select v-model="createPropModel('type', 'primary').value">
            <el-option label="主要" value="primary" />
            <el-option label="成功" value="success" />
            <el-option label="警告" value="warning" />
            <el-option label="危险" value="danger" />
            <el-option label="信息" value="info" />
            <el-option label="默认" value="default" />
          </el-select>
        </el-form-item>
        <el-form-item label="尺寸">
          <el-select v-model="createPropModel('size', 'default').value">
            <el-option label="默认" value="default" />
            <el-option label="大" value="large" />
            <el-option label="小" value="small" />
          </el-select>
        </el-form-item>
        <el-form-item label="朴素按钮">
          <el-switch v-model="createPropModel('plain', false).value" />
        </el-form-item>
        <el-form-item label="圆角">
          <el-switch v-model="createPropModel('round', false).value" />
        </el-form-item>
        <el-form-item label="圆形">
          <el-switch v-model="createPropModel('circle', false).value" />
        </el-form-item>
        <el-form-item label="图标">
          <el-input v-model="createPropModel('icon', '').value" placeholder="请输入图标名称" />
        </el-form-item>
        <el-form-item label="禁用">
          <el-switch v-model="createPropModel('disabled', false).value" />
        </el-form-item>
        <el-form-item label="加载中">
          <el-switch v-model="createPropModel('loading', false).value" />
        </el-form-item>
        <el-form-item label="自动聚焦">
          <el-switch v-model="createPropModel('autofocus', false).value" />
        </el-form-item>
        <el-form-item label="原生类型">
          <el-select v-model="createPropModel('nativeType', 'button').value">
            <el-option label="button" value="button" />
            <el-option label="submit" value="submit" />
            <el-option label="reset" value="reset" />
          </el-select>
        </el-form-item>
        <el-form-item label="链接地址">
          <el-input
            v-model="createPropModel('link', '').value"
            placeholder="按钮点击跳转的链接"
          />
        </el-form-item>
        <el-form-item label="链接目标">
          <el-select v-model="createPropModel('target', '_self').value">
            <el-option label="当前窗口" value="_self" />
            <el-option label="新窗口" value="_blank" />
            <el-option label="父窗口" value="_parent" />
            <el-option label="顶层窗口" value="_top" />
          </el-select>
        </el-form-item>
        <el-form-item label="按钮标签">
          <el-input
            v-model="createPropModel('tag', 'button').value"
            placeholder="HTML标签类型"
          />
        </el-form-item>
      </template>
      
      <!-- 输入框组件属性 -->
      <template v-else-if="component.type === 'input'">
        <el-form-item label="输入类型">
          <el-select v-model="createPropModel('inputType', 'text').value">
            <el-option label="文本" value="text" />
            <el-option label="密码" value="password" />
            <el-option label="数字" value="number" />
            <el-option label="邮箱" value="email" />
            <el-option label="电话" value="tel" />
            <el-option label="网址" value="url" />
            <el-option label="搜索" value="search" />
          </el-select>
        </el-form-item>
        <el-form-item label="占位符">
          <el-input v-model="createPropModel('placeholder', '请输入内容').value" />
        </el-form-item>
        <el-form-item label="默认值">
          <el-input v-model="createPropModel('value', '').value" />
        </el-form-item>
        <el-form-item label="最大长度">
          <el-input-number
            v-model="createPropModel('maxlength', 0).value"
            :min="0"
            placeholder="0表示无限制"
          />
        </el-form-item>
        <el-form-item label="最小长度">
          <el-input-number
            v-model="createPropModel('minlength', 0).value"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="尺寸">
          <el-select v-model="createPropModel('size', 'default').value">
            <el-option label="默认" value="default" />
            <el-option label="大" value="large" />
            <el-option label="小" value="small" />
          </el-select>
        </el-form-item>
        <el-form-item label="前缀图标">
          <el-input
            v-model="createPropModel('prefixIcon', '').value"
            placeholder="如 User、Lock 等"
          />
        </el-form-item>
        <el-form-item label="后缀图标">
          <el-input
            v-model="createPropModel('suffixIcon', '').value"
            placeholder="如 Search、View 等"
          />
        </el-form-item>
        <el-form-item label="可清空">
          <el-switch v-model="createPropModel('clearable', false).value" />
        </el-form-item>
        <el-form-item label="显示密码">
          <el-switch v-model="createPropModel('showPassword', false).value" />
        </el-form-item>
        <el-form-item label="显示字数统计">
          <el-switch v-model="createPropModel('showWordLimit', false).value" />
        </el-form-item>
        <el-form-item label="只读">
          <el-switch v-model="createPropModel('readonly', false).value" />
        </el-form-item>
        <el-form-item label="禁用">
          <el-switch v-model="createPropModel('disabled', false).value" />
        </el-form-item>
      </template>
      
      <!-- 文本组件属性 -->
      <template v-else-if="component.type === 'text'">
        <el-form-item label="文本内容">
          <el-input
            type="textarea"
            v-model="createPropModel('content', '文本内容').value"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="文本类型">
          <el-select v-model="createPropModel('type', 'primary').value">
            <el-option label="主要" value="primary" />
            <el-option label="成功" value="success" />
            <el-option label="警告" value="warning" />
            <el-option label="危险" value="danger" />
            <el-option label="信息" value="info" />
          </el-select>
        </el-form-item>
        <el-form-item label="文本尺寸">
          <el-select v-model="createPropModel('size', 'default').value">
            <el-option label="默认" value="default" />
            <el-option label="大" value="large" />
            <el-option label="小" value="small" />
          </el-select>
        </el-form-item>
        <el-form-item label="HTML标签">
          <el-select v-model="createPropModel('tag', 'span').value">
            <el-option label="span" value="span" />
            <el-option label="div" value="div" />
            <el-option label="p" value="p" />
            <el-option label="h1" value="h1" />
            <el-option label="h2" value="h2" />
            <el-option label="h3" value="h3" />
            <el-option label="h4" value="h4" />
            <el-option label="h5" value="h5" />
            <el-option label="h6" value="h6" />
          </el-select>
        </el-form-item>
        <el-form-item label="字体大小">
          <el-input
            v-model="createPropModel('fontSize', '14px').value"
            placeholder="如 14px, 1.2em, 16pt"
          />
        </el-form-item>
        <el-form-item label="字体粗细">
          <el-select v-model="createPropModel('fontWeight', 'normal').value">
            <el-option label="正常" value="normal" />
            <el-option label="粗体" value="bold" />
            <el-option label="细体" value="lighter" />
            <el-option label="更粗" value="bolder" />
            <el-option label="100" value="100" />
            <el-option label="200" value="200" />
            <el-option label="300" value="300" />
            <el-option label="400" value="400" />
            <el-option label="500" value="500" />
            <el-option label="600" value="600" />
            <el-option label="700" value="700" />
            <el-option label="800" value="800" />
            <el-option label="900" value="900" />
          </el-select>
        </el-form-item>
        <el-form-item label="文本颜色">
          <el-color-picker
            v-model="createPropModel('color', '#303133').value"
            show-alpha
          />
        </el-form-item>
        <el-form-item label="文本对齐">
          <el-select v-model="createPropModel('textAlign', 'left').value">
            <el-option label="左对齐" value="left" />
            <el-option label="居中" value="center" />
            <el-option label="右对齐" value="right" />
            <el-option label="两端对齐" value="justify" />
          </el-select>
        </el-form-item>
        <el-form-item label="文本截断">
          <el-switch v-model="createPropModel('truncated', false).value" />
        </el-form-item>
        <el-form-item label="行数限制" v-if="component.props?.truncated">
          <el-input-number
            v-model="createPropModel('lineClamp', 1).value"
            :min="1"
            :max="10"
          />
        </el-form-item>
      </template>
      
      <!-- 图片组件属性 -->
      <template v-else-if="component.type === 'image'">
        <el-form-item label="图片地址">
          <el-input
            v-model="createPropModel('src', 'https://via.placeholder.com/150').value"
            placeholder="请输入图片URL"
          />
        </el-form-item>
        <el-form-item label="替代文本">
          <el-input
            v-model="createPropModel('alt', '图片').value"
            placeholder="图片加载失败时显示的文本"
          />
        </el-form-item>
        <el-form-item label="适应方式">
          <el-select v-model="createPropModel('fit', 'fill').value">
            <el-option label="填充" value="fill" />
            <el-option label="包含" value="contain" />
            <el-option label="覆盖" value="cover" />
            <el-option label="无缩放" value="none" />
            <el-option label="缩小" value="scale-down" />
          </el-select>
        </el-form-item>
        <el-form-item label="加载方式">
          <el-select v-model="createPropModel('loading', 'eager').value">
            <el-option label="立即加载" value="eager" />
            <el-option label="懒加载" value="lazy" />
          </el-select>
        </el-form-item>
        <el-form-item label="懒加载">
          <el-switch v-model="createPropModel('lazy', false).value" />
        </el-form-item>
        <el-form-item label="预览图片列表">
          <div class="preview-list-editor">
            <div
              v-for="(url, index) in component.props?.previewSrcList || []"
              :key="index"
              class="preview-item"
            >
              <el-input
                :value="url"
                @input="updatePreviewUrl(index, $event)"
                placeholder="预览图片URL"
                size="small"
              />
              <el-button
                @click="removePreviewUrl(index)"
                size="small"
                type="danger"
                icon="Delete"
              />
            </div>
            <el-button
              @click="addPreviewUrl"
              size="small"
              type="primary"
              icon="Plus"
            >
              添加预览图片
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="预览层级">
          <el-input-number
            v-model="createPropModel('zIndex', 2000).value"
            :min="1"
          />
        </el-form-item>
        <el-form-item label="初始预览索引">
          <el-input-number
            v-model="createPropModel('initialIndex', 0).value"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="ESC关闭预览">
          <el-switch v-model="createPropModel('closeOnPressEscape', true).value" />
        </el-form-item>
        <el-form-item label="预览传送">
          <el-switch v-model="createPropModel('previewTeleported', true).value" />
        </el-form-item>
      </template>
      
      <!-- 容器组件属性 -->
      <template v-else-if="component.type === 'container'">
        <el-form-item label="布局方向">
          <el-select 
            :value="component.props.direction"
            @change="updateProp('direction', $event)"
          >
            <el-option label="垂直" value="vertical" />
            <el-option label="水平" value="horizontal" />
          </el-select>
        </el-form-item>
      </template>
      
      <!-- 选择器组件属性 -->
      <template v-else-if="component.type === 'select'">
        <el-form-item label="占位符">
          <el-input v-model="createPropModel('placeholder', '请选择').value" />
        </el-form-item>
        <el-form-item label="选项配置">
          <div class="select-options">
            <div 
              v-for="(option, index) in component.props.options"
              :key="index"
              class="option-item"
            >
              <el-input 
                :value="option.label"
                @input="updateOption(index, 'label', $event)"
                placeholder="显示文本"
                size="small"
              />
              <el-input 
                :value="option.value"
                @input="updateOption(index, 'value', $event)"
                placeholder="选项值"
                size="small"
              />
              <el-button 
                @click="removeOption(index)"
                size="small"
                type="danger"
                icon="Delete"
              />
            </div>
            <el-button 
              @click="addOption"
              size="small"
              type="primary"
              icon="Plus"
            >
              添加选项
            </el-button>
          </div>
        </el-form-item>
      </template>
      
      <!-- 标题栏组件属性 -->
      <template v-else-if="component.type === 'window-titlebar'">
        <el-form-item label="标题">
          <el-input :value="component.props?.title || ''" @input="updateProp('title', $event)" />
        </el-form-item>
        <el-form-item label="图标URL">
          <el-input :value="component.props?.icon || ''" @input="updateProp('icon', $event)" placeholder="请输入图片地址" />
        </el-form-item>
        <el-form-item label="显示暗黑按钮">
          <el-switch :model-value="!!component.props?.showDark" @update:model-value="(val: boolean) => updateProp('showDark', val)" />
        </el-form-item>
        <el-form-item label="显示最小化按钮">
          <el-switch :model-value="!!component.props?.showMin" @update:model-value="(val: boolean) => updateProp('showMin', val)" />
        </el-form-item>
        <el-form-item label="显示最大化按钮">
          <el-switch :model-value="!!component.props?.showMax" @update:model-value="(val: boolean) => updateProp('showMax', val)" />
        </el-form-item>
        <el-form-item label="显示关闭按钮">
          <el-switch :model-value="!!component.props?.showClose" @update:model-value="(val: boolean) => updateProp('showClose', val)" />
        </el-form-item>
        <el-form-item label="暗黑模式">
          <el-switch :model-value="!!component.props?.dark" @update:model-value="(val: boolean) => updateProp('dark', val)" />
        </el-form-item>
        <el-form-item label="背景色/渐变">
          <el-input :value="component.props?.background || ''" @input="updateProp('background', $event)" placeholder="如 #fff 或 linear-gradient(...)" />
        </el-form-item>
        <el-form-item label="标题颜色">
          <el-input :value="component.props?.titleColor || ''" @input="updateProp('titleColor', $event)" placeholder="如 #333" />
        </el-form-item>
        <el-form-item label="高度">
          <el-input-number :value="component.props?.height || 36" @change="(val: boolean) => updateProp('height', val)" :min="24" :max="120" />
        </el-form-item>
        <el-form-item label="圆角">
          <el-input-number :value="component.props?.radius || 6" @change="(val: boolean) => updateProp('radius', val)" :min="0" :max="32" />
        </el-form-item>
      </template>

      <!-- 多行文本框组件属性 -->
      <template v-else-if="component.type === 'textarea'">
        <el-form-item label="占位符">
          <el-input
            :value="component.props?.placeholder || '请输入内容'"
            @input="updateProp('placeholder', $event)"
          />
        </el-form-item>
        <el-form-item label="行数">
          <el-input-number
            :value="component.props?.rows || 3"
            @change="updateProp('rows', $event)"
            :min="1"
            :max="20"
          />
        </el-form-item>
        <el-form-item label="最大长度">
          <el-input-number
            :value="component.props?.maxlength || 0"
            @change="updateProp('maxlength', $event)"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="显示字数统计">
          <el-switch :model-value="!!component.props?.showWordLimit" @update:model-value="(val: boolean) => updateProp('showWordLimit', val)" />
        </el-form-item>
        <el-form-item label="禁用">
          <el-switch :model-value="!!component.props?.disabled" @update:model-value="(val: boolean) => updateProp('disabled', val)" />
        </el-form-item>
      </template>

      <!-- 复选框组件属性 -->
      <template v-else-if="component.type === 'checkbox'">
        <el-form-item label="标签文本">
          <el-input
            :value="component.props?.label || '复选框'"
            @input="updateProp('label', $event)"
          />
        </el-form-item>
        <el-form-item label="默认选中">
          <el-switch :model-value="!!component.props?.checked" @update:model-value="(val: boolean) => updateProp('checked', val)" />
        </el-form-item>
        <el-form-item label="半选状态">
          <el-switch :model-value="!!component.props?.indeterminate" @update:model-value="(val: boolean) => updateProp('indeterminate', val)" />
        </el-form-item>
        <el-form-item label="尺寸">
          <el-select
            :value="component.props?.size || 'default'"
            @change="updateProp('size', $event)"
          >
            <el-option label="默认" value="default" />
            <el-option label="大" value="large" />
            <el-option label="小" value="small" />
          </el-select>
        </el-form-item>
        <el-form-item label="禁用">
          <el-switch :model-value="!!component.props?.disabled" @update:model-value="(val: boolean) => updateProp('disabled', val)" />
        </el-form-item>
      </template>

      <!-- 单选框组件属性 -->
      <template v-else-if="component.type === 'radio'">
        <el-form-item label="标签文本">
          <el-input
            :value="component.props?.label || '单选框'"
            @input="updateProp('label', $event)"
          />
        </el-form-item>
        <el-form-item label="选项值">
          <el-input
            :value="component.props?.value || 'option1'"
            @input="updateProp('value', $event)"
          />
        </el-form-item>
        <el-form-item label="尺寸">
          <el-select
            :value="component.props?.size || 'default'"
            @change="updateProp('size', $event)"
          >
            <el-option label="默认" value="default" />
            <el-option label="大" value="large" />
            <el-option label="小" value="small" />
          </el-select>
        </el-form-item>
        <el-form-item label="禁用">
          <el-switch :model-value="!!component.props?.disabled" @update:model-value="(val: boolean) => updateProp('disabled', val)" />
        </el-form-item>
      </template>

      <!-- 开关组件属性 -->
      <template v-else-if="component.type === 'switch'">
        <el-form-item label="默认值">
          <el-switch :model-value="!!component.props?.value" @update:model-value="(val: boolean) => updateProp('value', val)" />
        </el-form-item>
        <el-form-item label="开启文字">
          <el-input
            :value="component.props?.activeText || ''"
            @input="updateProp('activeText', $event)"
            placeholder="开启时显示的文字"
          />
        </el-form-item>
        <el-form-item label="关闭文字">
          <el-input
            :value="component.props?.inactiveText || ''"
            @input="updateProp('inactiveText', $event)"
            placeholder="关闭时显示的文字"
          />
        </el-form-item>
        <el-form-item label="开启颜色">
          <el-input
            :value="component.props?.activeColor || ''"
            @input="updateProp('activeColor', $event)"
            placeholder="如 #13ce66"
          />
        </el-form-item>
        <el-form-item label="关闭颜色">
          <el-input
            :value="component.props?.inactiveColor || ''"
            @input="updateProp('inactiveColor', $event)"
            placeholder="如 #ff4949"
          />
        </el-form-item>
        <el-form-item label="尺寸">
          <el-select
            :value="component.props?.size || 'default'"
            @change="updateProp('size', $event)"
          >
            <el-option label="默认" value="default" />
            <el-option label="大" value="large" />
            <el-option label="小" value="small" />
          </el-select>
        </el-form-item>
        <el-form-item label="禁用">
          <el-switch :model-value="!!component.props?.disabled" @update:model-value="(val: boolean) => updateProp('disabled', val)" />
        </el-form-item>
      </template>

      <!-- 滑块组件属性 -->
      <template v-else-if="component.type === 'slider'">
        <el-form-item label="默认值">
          <el-input-number
            :value="component.props?.value || 50"
            @change="updateProp('value', $event)"
          />
        </el-form-item>
        <el-form-item label="最小值">
          <el-input-number
            :value="component.props?.min || 0"
            @change="updateProp('min', $event)"
          />
        </el-form-item>
        <el-form-item label="最大值">
          <el-input-number
            :value="component.props?.max || 100"
            @change="updateProp('max', $event)"
          />
        </el-form-item>
        <el-form-item label="步长">
          <el-input-number
            :value="component.props?.step || 1"
            @change="updateProp('step', $event)"
            :min="1"
          />
        </el-form-item>
        <el-form-item label="显示输入框">
          <el-switch :model-value="!!component.props?.showInput" @update:model-value="(val: boolean) => updateProp('showInput', val)" />
        </el-form-item>
        <el-form-item label="显示间断点">
          <el-switch :model-value="!!component.props?.showStops" @update:model-value="(val: boolean) => updateProp('showStops', val)" />
        </el-form-item>
        <el-form-item label="范围选择">
          <el-switch :model-value="!!component.props?.range" @update:model-value="(val: boolean) => updateProp('range', val)" />
        </el-form-item>
        <el-form-item label="禁用">
          <el-switch :model-value="!!component.props?.disabled" @update:model-value="(val: boolean) => updateProp('disabled', val)" />
        </el-form-item>
      </template>

      <!-- 日期选择器组件属性 -->
      <template v-else-if="component.type === 'date-picker'">
        <el-form-item label="占位符">
          <el-input
            :value="component.props?.placeholder || '选择日期'"
            @input="updateProp('placeholder', $event)"
          />
        </el-form-item>
        <el-form-item label="选择器类型">
          <el-select
            :value="component.props?.type || 'date'"
            @change="updateProp('type', $event)"
          >
            <el-option label="日期" value="date" />
            <el-option label="日期时间" value="datetime" />
            <el-option label="日期范围" value="daterange" />
            <el-option label="日期时间范围" value="datetimerange" />
            <el-option label="月份" value="month" />
            <el-option label="年份" value="year" />
          </el-select>
        </el-form-item>
        <el-form-item label="可清空">
          <el-switch :model-value="!!component.props?.clearable" @update:model-value="(val: boolean) => updateProp('clearable', val)" />
        </el-form-item>
        <el-form-item label="尺寸">
          <el-select
            :value="component.props?.size || 'default'"
            @change="updateProp('size', $event)"
          >
            <el-option label="默认" value="default" />
            <el-option label="大" value="large" />
            <el-option label="小" value="small" />
          </el-select>
        </el-form-item>
        <el-form-item label="禁用">
          <el-switch :model-value="!!component.props?.disabled" @update:model-value="(val: boolean) => updateProp('disabled', val)" />
        </el-form-item>
      </template>

      <!-- 时间选择器组件属性 -->
      <template v-else-if="component.type === 'time-picker'">
        <el-form-item label="占位符">
          <el-input
            :value="component.props?.placeholder || '选择时间'"
            @input="updateProp('placeholder', $event)"
          />
        </el-form-item>
        <el-form-item label="可清空">
          <el-switch :model-value="!!component.props?.clearable" @update:model-value="(val: boolean) => updateProp('clearable', val)" />
        </el-form-item>
        <el-form-item label="尺寸">
          <el-select
            :value="component.props?.size || 'default'"
            @change="updateProp('size', $event)"
          >
            <el-option label="默认" value="default" />
            <el-option label="大" value="large" />
            <el-option label="小" value="small" />
          </el-select>
        </el-form-item>
        <el-form-item label="禁用">
          <el-switch :model-value="!!component.props?.disabled" @update:model-value="(val: boolean) => updateProp('disabled', val)" />
        </el-form-item>
      </template>

      <!-- 评分组件属性 -->
      <template v-else-if="component.type === 'rate'">
        <el-form-item label="默认值">
          <el-input-number
            :value="component.props?.value || 0"
            @change="updateProp('value', $event)"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="最大分值">
          <el-input-number
            :value="component.props?.max || 5"
            @change="updateProp('max', $event)"
            :min="1"
            :max="10"
          />
        </el-form-item>
        <el-form-item label="允许半选">
          <el-switch :model-value="!!component.props?.allowHalf" @update:model-value="(val: boolean) => updateProp('allowHalf', val)" />
        </el-form-item>
        <el-form-item label="显示辅助文字">
          <el-switch :model-value="!!component.props?.showText" @update:model-value="(val: boolean) => updateProp('showText', val)" />
        </el-form-item>
        <el-form-item label="显示当前分数">
          <el-switch :model-value="!!component.props?.showScore" @update:model-value="(val: boolean) => updateProp('showScore', val)" />
        </el-form-item>
        <el-form-item label="禁用">
          <el-switch :model-value="!!component.props?.disabled" @update:model-value="(val: boolean) => updateProp('disabled', val)" />
        </el-form-item>
      </template>

      <!-- 颜色选择器组件属性 -->
      <template v-else-if="component.type === 'color-picker'">
        <el-form-item label="默认颜色">
          <el-input
            :value="component.props?.value || '#409EFF'"
            @input="updateProp('value', $event)"
            placeholder="如 #409EFF"
          />
        </el-form-item>
        <el-form-item label="支持透明度">
          <el-switch :model-value="!!component.props?.showAlpha" @update:model-value="(val: boolean) => updateProp('showAlpha', val)" />
        </el-form-item>
        <el-form-item label="颜色格式">
          <el-select
            :value="component.props?.colorFormat || 'hex'"
            @change="updateProp('colorFormat', $event)"
          >
            <el-option label="HEX" value="hex" />
            <el-option label="RGB" value="rgb" />
            <el-option label="HSL" value="hsl" />
            <el-option label="HSV" value="hsv" />
          </el-select>
        </el-form-item>
        <el-form-item label="尺寸">
          <el-select
            :value="component.props?.size || 'default'"
            @change="updateProp('size', $event)"
          >
            <el-option label="默认" value="default" />
            <el-option label="大" value="large" />
            <el-option label="小" value="small" />
          </el-select>
        </el-form-item>
        <el-form-item label="禁用">
          <el-switch :model-value="!!component.props?.disabled" @update:model-value="(val: boolean) => updateProp('disabled', val)" />
        </el-form-item>
      </template>

      <!-- 默认提示 -->
      <template v-else>
        <el-form-item>
          <el-alert
            title="暂无可编辑的属性"
            description="该组件类型暂不支持属性编辑"
            type="info"
            :closable="false"
          />
        </el-form-item>
      </template>
      
    </el-form>
    
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ComponentData } from '@/stores/designer'

// 组件属性
interface Props {
  component: ComponentData
}

// 事件定义
interface Emits {
  (e: 'update', props: Record<string, any>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 创建双向绑定的计算属性
const createPropModel = (key: string, defaultValue: any = '') => {
  return computed({
    get: () => props.component.props?.[key] ?? defaultValue,
    set: (value: any) => updateProp(key, value)
  })
}

// 更新单个属性
const updateProp = (key: string, value: any) => {
  const newProps = { ...props.component.props }
  newProps[key] = value
  emit('update', newProps)
}

// 更新选择器选项
const updateOption = (index: number, field: string, value: string) => {
  const options = [...props.component.props.options]
  options[index] = { ...options[index], [field]: value }
  updateProp('options', options)
}

// 添加选择器选项
const addOption = () => {
  const options = [...(props.component.props.options || [])]
  options.push({ label: '新选项', value: `option_${options.length + 1}` })
  updateProp('options', options)
}

// 删除选择器选项
const removeOption = (index: number) => {
  const options = [...props.component.props.options]
  options.splice(index, 1)
  updateProp('options', options)
}

// 更新预览图片URL
const updatePreviewUrl = (index: number, value: string) => {
  const previewSrcList = [...(props.component.props.previewSrcList || [])]
  previewSrcList[index] = value
  updateProp('previewSrcList', previewSrcList)
}

// 添加预览图片
const addPreviewUrl = () => {
  const previewSrcList = [...(props.component.props.previewSrcList || [])]
  previewSrcList.push('https://via.placeholder.com/300')
  updateProp('previewSrcList', previewSrcList)
}

// 删除预览图片
const removePreviewUrl = (index: number) => {
  const previewSrcList = [...(props.component.props.previewSrcList || [])]
  previewSrcList.splice(index, 1)
  updateProp('previewSrcList', previewSrcList)
}
</script>

<style scoped>
/* 属性编辑器容器 */
.props-editor {
  padding: 0;
}

/* 选择器选项编辑 */
.select-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.option-item .el-input {
  flex: 1;
}

/* 预览列表编辑器 */
.preview-list-editor {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.preview-item .el-input {
  flex: 1;
}

/* 表单项样式调整 */
.el-form-item {
  margin-bottom: 16px;
}

.el-form-item:last-child {
  margin-bottom: 0;
}
</style>