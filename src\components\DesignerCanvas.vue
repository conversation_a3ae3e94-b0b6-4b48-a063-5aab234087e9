<template>
  <div class="canvas-container">
    
    
    <!-- 窗口切换栏 - 独立容器 -->
    <div class="window-tabs-container">
      <el-tabs v-model="activeWindowId" type="card" class="window-tabs" @tab-remove="removeWindow" @tab-click="handleTabClick" addable @tab-add="addWindow">
        <el-tab-pane
          v-for="win in windows"
          :key="win.id"
          :label="win.name"
          :name="win.id"
          :closable="windows.length > 1"
        />
      </el-tabs>
    </div>
    <!-- 画布工具栏 -->
    <div class="canvas-toolbar">
      <div class="toolbar-left">
        <el-button-group size="small">
          <!-- 撤销按钮 -->
          <el-tooltip content="撤销 (Ctrl+Z)" placement="bottom">
            <el-button :icon="RefreshLeft" @click="handleUndo" :disabled="!canUndo" />
          </el-tooltip>
          
          <!-- 重做按钮 -->
          <el-tooltip content="重做 (Ctrl+Y)" placement="bottom">
            <el-button :icon="RefreshRight" @click="handleRedo" :disabled="!canRedo" />
          </el-tooltip>
        </el-button-group>
        
        <el-button-group size="small" style="margin-left: 12px;">
          <!-- 智能对齐按钮改为纯图标按钮 -->
          <el-tooltip :content="showAlignLines ? '关闭智能对齐（辅助线/吸附）' : '开启智能对齐（辅助线/吸附）'" placement="bottom">
            <el-button :icon="MagicStick" :type="showAlignLines ? 'primary' : 'danger'" @click="autoAlign"  />
          </el-tooltip>
          
          <!-- 网格显示切换按钮 -->
          <el-tooltip :content="showGrid ? '隐藏网格' : '显示网格'" placement="bottom">
            <el-button :icon="Grid" :type="showGrid ? 'primary' : 'danger'" @click="toggleGrid" />
          </el-tooltip>
        </el-button-group>
        <!-- 对齐工具按钮组 -->
        <el-button-group size="small" style="margin-left: 12px;">
          <el-tooltip content="左对齐" placement="bottom"><el-button :icon="IconAlignLeft" @click="alignLeft" :disabled="multiSelectedIds.length < 2" /></el-tooltip>
          <el-tooltip content="水平居中" placement="bottom"><el-button :icon="IconAlignCenter" @click="alignCenterH" :disabled="multiSelectedIds.length < 2" /></el-tooltip>
          <el-tooltip content="右对齐" placement="bottom"><el-button :icon="IconAlignRight" @click="alignRight" :disabled="multiSelectedIds.length < 2" /></el-tooltip>
          <el-tooltip content="顶对齐" placement="bottom"><el-button :icon="IconAlignTop" @click="alignTop" :disabled="multiSelectedIds.length < 2" /></el-tooltip>
          <el-tooltip content="垂直居中" placement="bottom"><el-button :icon="IconAlignVCenter" @click="alignCenterV" :disabled="multiSelectedIds.length < 2" /></el-tooltip>
          <el-tooltip content="底对齐" placement="bottom"><el-button :icon="IconAlignBottom" @click="alignBottom" :disabled="multiSelectedIds.length < 2" /></el-tooltip>
          <el-tooltip content="等宽" placement="bottom"><el-button :icon="IconSameWidth" @click="alignWidth" :disabled="multiSelectedIds.length < 2" /></el-tooltip>
          <el-tooltip content="等高" placement="bottom"><el-button :icon="IconSameHeight" @click="alignHeight" :disabled="multiSelectedIds.length < 2" /></el-tooltip>
          <el-tooltip content="水平分布" placement="bottom"><el-button :icon="IconDistributeHorizontal" @click="distributeH" :disabled="multiSelectedIds.length < 3" /></el-tooltip>
          <el-tooltip content="垂直分布" placement="bottom"><el-button :icon="IconDistributeVertical" @click="distributeV" :disabled="multiSelectedIds.length < 3" /></el-tooltip>
        </el-button-group>
      </div>
      

      
      <div class="toolbar-right">
        <el-button-group size="small">
          <!-- 移除缩小、放大按钮及缩放比例显示 -->
        </el-button-group>
      </div>
    </div>
    <!-- 画布主体区域,不进行相关功能和逻辑,因为我们的主窗口是下面的模拟窗口容器 -->
    <div 
      ref="canvasWrapper"
      class="canvas-wrapper"
      :class="{ 'show-grid': showGrid }"
      @click="handleCanvasClick"
    >
            <!-- 模拟窗口容器 - 使用Element Plus卡片组件 -->
      <el-card
        class="window-container"
        :class="{
          'window-selected': isWindowSelected,
          'window-dark-mode': activeWindow.titleBarConfig?.isDarkMode
        }"
        :style="windowContainerDynamicStyle"
        :body-style="cardBodyStyle"
        :shadow="activeWindow.cardConfig?.shadow || 'always'"
        @click="handleWindowClick"
        @drop="handleDrop"
        @dragover="handleDragOver"
      >
        <!-- 只有normal类型窗口才包含header插槽 -->
        <template v-if="activeWindow.windowType === 'normal'" #header>
          <div
            class="integrated-title-bar"
            :class="{ 'dark-mode': activeWindow.titleBarConfig?.isDarkMode }"
            :style="{
              background: activeWindow.titleBarConfig?.backgroundColor || undefined,
              color: activeWindow.titleBarConfig?.titleColor || undefined,
              height: (activeWindow.titleBarConfig?.height || 36) + 'px'
            }"
          >
            <!-- 左侧：应用LOGO和标题 -->
            <div class="title-bar-left">
              <!-- 应用LOGO -->
              <div
                v-if="activeWindow.titleBarConfig?.showLogo && activeWindow.titleBarConfig?.logoPath"
                class="app-logo-container"
              >
                <img :src="activeWindow.titleBarConfig.logoPath" alt="应用图标" class="app-logo" />
              </div>
              <!-- 默认图标（当没有LOGO时） -->
              <div v-else-if="activeWindow.titleBarConfig?.showLogo" class="default-icon">
                <el-icon :size="18"><Monitor /></el-icon>
              </div>
              <!-- 窗口标题 -->
              <span class="window-title">
                {{ activeWindow.titleBarConfig?.title || activeWindow.name || '无标题窗口' }}
              </span>
            </div>

            <!-- 右侧：控制按钮 -->
            <div class="title-bar-controls">
              <!-- 暗黑模式切换按钮 -->
              <el-tooltip v-if="activeWindow.titleBarConfig?.showDarkToggle" content="切换暗黑模式" placement="bottom">
                <button
                  class="title-control-btn dark-toggle-btn"
                  :class="{ active: activeWindow.titleBarConfig?.isDarkMode }"
                  @click.stop="handleTitleBarDarkToggle"
                >
                  <el-icon :size="14">
                    <component :is="activeWindow.titleBarConfig?.isDarkMode ? Sunny : Moon" />
                  </el-icon>
                </button>
              </el-tooltip>

              <!-- 最小化按钮 -->
              <el-tooltip v-if="activeWindow.titleBarConfig?.showMinimize" content="最小化" placement="bottom">
                <button class="title-control-btn minimize-btn" @click.stop="handleTitleBarMinimize">
                  <el-icon :size="14"><Minus /></el-icon>
                </button>
              </el-tooltip>

              <!-- 最大化按钮 -->
              <el-tooltip v-if="activeWindow.titleBarConfig?.showMaximize" content="最大化" placement="bottom">
                <button class="title-control-btn maximize-btn" @click.stop="handleTitleBarMaximize">
                  <el-icon :size="14"><FullScreen /></el-icon>
                </button>
              </el-tooltip>

              <!-- 关闭按钮 -->
              <el-tooltip v-if="activeWindow.titleBarConfig?.showClose" content="关闭" placement="bottom">
                <button class="title-control-btn close-btn" @click.stop="handleTitleBarClose">
                  <el-icon :size="14"><Close /></el-icon>
                </button>
              </el-tooltip>
            </div>
          </div>
        </template>
        <!-- 在el-card__body上绑定框选事件 -->
        <div
          class="card-body-wrapper"
          :class="{ 'window-dark-mode': activeWindow.titleBarConfig?.isDarkMode }"
          @mousedown="handleWindowMouseDown"
          @mousemove="onWindowMouseMove"
          @mouseup="onWindowMouseUp"
          @click="handleBodyWrapperClick"
        >
        <!-- 框选选框（移到window-container内） -->
        <div v-if="selectionBox.visible" class="selection-box"
          :style="selectionBoxStyle"
        ></div>
        
        <!-- 辅助线渲染 -->
        <div v-if="showAlignLines" v-for="line in allAlignLines" :key="line.type + line.position" 
          :class="['align-line', line.type]"
          :style="line.type === 'vertical' ? { left: line.position + 'px' } : { top: line.position + 'px' }"
        ></div>
        
        <!-- 组件渲染和网格线直接放在 window-container 内部 -->
        <CanvasComponent
          v-for="component in components"
          :key="component.id"
          :component-data="component"
          :is-selected="selectedComponentId === component.id"
          @select="(id, e) => handleSelectComponent(id, e)"
          @update="handleUpdateComponent"
          @align-lines="handleAlignLines"
        />
        
        <!-- 空状态显示 - 放在组件后面，确保不影响框选 -->
        <template v-if="components.length === 0">
          <div class="empty-state-container">
            <el-empty description="暂无组件" :image-size="80" />
          </div>
        </template>
        <!-- 移除网格渲染 -->
        </div>
      </el-card>
      
      <!-- 8点缩放控制 - 放在el-card外部以确保正确定位 -->
      <template v-if="isWindowSelected">
        <div v-for="dir in resizeDirs" :key="dir" class="resize-handle window-resize-handle" :class="'resize-' + dir"
          :style="getResizeHandleStyle(dir)"
          @mousedown.stop="e => onWindowResizeMouseDown(e, dir)"
        ></div>
      </template>
      
    </div>
    
    <!-- 画布信息栏 -->
    <div class="canvas-info">
      <div class="info-left">
        <el-tag size="small" effect="plain">窗口: {{ activeWindowName }}</el-tag>
        <el-tag size="small" effect="plain">窗口尺寸: {{ windowContainerDynamicStyle.width }} × {{ windowContainerDynamicStyle.height }}</el-tag>
        <el-tag size="small" effect="plain">组件数量: {{ components.length }}</el-tag>
      </div>
      
      <div class="info-right">
        <el-tag v-if="selectedComponent && 'x' in selectedComponent" size="small" type="success">
          选中: {{ selectedComponent.name }} ({{ selectedComponent.x }}, {{ selectedComponent.y }}, {{ selectedComponent.width }}×{{ selectedComponent.height }})
        </el-tag>
        <el-tag v-else-if="selectedComponent && 'windowType' in selectedComponent" size="small" type="primary">
          选中窗口: {{ selectedComponent.name }} (类型: {{ selectedComponent.windowType === 'normal' ? '有标题栏' : '无标题栏' }})
        </el-tag>
        <el-tag v-else size="small" type="info">未选择组件</el-tag>
      </div>
    </div>
    
  </div>
</template>

<script setup lang="ts">
import { ref, computed, provide, reactive, watch, onMounted, onBeforeUnmount } from 'vue'
import { useDesignerStore } from '@/stores/designer'
import { storeToRefs } from 'pinia'
import { ElMessage } from 'element-plus'
import {
  MagicStick,
  Grid,
  RefreshLeft,
  RefreshRight,
  Close,
  FullScreen,
  Minus,
  Moon,
  Sunny,
  Monitor
} from '@element-plus/icons-vue'
import CanvasComponent from './CanvasComponent.vue'
import {
  IconAlignLeft,
  IconAlignCenter,
  IconAlignRight,
  IconAlignTop,
  IconAlignBottom,
  IconAlignVCenter,
  IconSameWidth,
  IconSameHeight,
  IconDistributeHorizontal,
  IconDistributeVertical
} from './AlignIcons'
import { nanoid } from 'nanoid'
import { useRouter } from 'vue-router'

// 获取设计器状态
const designerStore = useDesignerStore()
const { 
  components, 
  selectedComponentId, 
  canvasConfig,
  isPreviewMode,
  windows,
  activeWindowId,
  activeWindow,
  windowConfig,
  showGrid,
  showAlignLines,
  canUndo,
  canRedo
} = storeToRefs(designerStore)

// 获取路由实例
const router = useRouter()

// 获取选中的组件
const selectedComponent = computed(() => designerStore.getSelectedComponent())

// 计算当前窗口名称
const activeWindowName = computed(() => {
  const window = windows.value.find(w => w.id === activeWindowId.value)
  return window?.name || '主窗口'
})

// 多选组件id列表 - 移到前面以便模板访问
const multiSelectedIds = ref<string[]>([])
provide('multiSelectedIds', multiSelectedIds)

// 画布DOM引用
const canvasWrapper = ref<HTMLElement>()
const canvas = ref<HTMLElement>()

// 缩放级别
const zoomLevel = ref(100)

// 窗口容器的动态样式 - 包含卡片外观配置
const windowContainerDynamicStyle = computed(() => {
  const cardConfig = activeWindow.value.cardConfig || {}
  return {
    /* 保持绝对定位 */
    position: 'absolute' as const,
    left: '0',
    top: '0',
    width: windowConfig.value.width + 'px',
    height: windowConfig.value.height + 'px',
    zIndex: 2,
    /* 卡片外观配置 */
    borderRadius: (cardConfig.borderRadius || 6) + 'px',
    backgroundColor: cardConfig.backgroundColor || '#ffffff',
    borderColor: cardConfig.borderColor || '#ebeef5',
    borderWidth: (cardConfig.borderWidth || 1) + 'px',
    borderStyle: 'solid'
  }
})

// 卡片body样式
const cardBodyStyle = computed(() => {
  const cardConfig = activeWindow.value.cardConfig || {}
  return {
    padding: (cardConfig.bodyPadding || 20) + 'px',
    height: '100%',
    position: 'relative'
  }
})

// provide window-container宽高给子组件
provide('windowWidth', computed(() => windowConfig.value.width))
provide('windowHeight', computed(() => windowConfig.value.height))

// provide 模拟窗口暗黑模式状态给子组件
provide('windowDarkMode', computed(() => activeWindow.value.titleBarConfig?.isDarkMode ?? false))

// 智能对齐
const autoAlign = () => {
  designerStore.toggleAlignLines()
  if (!showAlignLines.value) alignLinesMap.clear()
}

// 网格显示切换
const toggleGrid = () => {
  designerStore.toggleGrid()
}

// 撤销操作
const handleUndo = () => {
  // TODO: 实现撤销功能
  ElMessage.info('撤销功能开发中...')
}

// 重做操作
const handleRedo = () => {
  // TODO: 实现重做功能
  ElMessage.info('重做功能开发中...')
}



// 处理拖拽悬停
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'copy'
}

// 处理组件拖放
const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  try {
    const dragDataStr = event.dataTransfer?.getData('application/json')
    if (!dragDataStr) return
    const dragData = JSON.parse(dragDataStr)
    
    // 计算放置位置（window-container 作为画布）
    const containerRect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    const scale = zoomLevel.value / 100
    const x = (event.clientX - containerRect.left) / scale
    const y = (event.clientY - containerRect.top) / scale
    
    // 网格对齐
    const gridSize = canvasConfig.value.gridSize
    const alignedX = Math.round(x / gridSize) * gridSize
    const alignedY = Math.round(y / gridSize) * gridSize
    
    // 获取当前窗口尺寸
    const maxW = windowConfig.value.width
    const maxH = windowConfig.value.height
    
    // 确保组件不会超出窗口边界
    let finalX = Math.max(0, Math.min(alignedX, maxW - dragData.width))
    let finalY = Math.max(0, Math.min(alignedY, maxH - dragData.height))
    
    // 如果组件尺寸超过窗口，则调整尺寸
    let finalWidth = dragData.width
    let finalHeight = dragData.height
    
    if (finalWidth > maxW) {
      finalWidth = maxW
      finalX = 0
    }
    if (finalHeight > maxH) {
      finalHeight = maxH
      finalY = 0
    }
    
    // 添加组件到设计器
    designerStore.addComponent({
      ...dragData,
      x: finalX,
      y: finalY,
      width: finalWidth,
      height: finalHeight
    })
    
    ElMessage.success(`已添加${dragData.name}组件`)
  } catch (error) {
    console.error('处理拖放失败:', error)
    ElMessage.error('添加组件失败')
  }
}

// 清空所有辅助线
const clearAllAlignLines = () => alignLinesMap.clear()

// 监听选中组件变化，未拖拽/缩放时清空辅助线
watch(selectedComponentId, () => {
  clearAllAlignLines()
})

// 画布点击时清空辅助线和取消窗口选中
const handleCanvasClick = (event: MouseEvent) => {
  // 检查是否点击的是画布空白区域（canvas-wrapper）
  if (event.target === canvasWrapper.value) {
    // 取消窗口选中
    deselectWindow()
    // 取消组件选中
    designerStore.setSelectedComponent(null)
    multiSelectedIds.value = []
    // 清空辅助线
    clearAllAlignLines()
  }
}

// 处理窗口点击，选中窗口本身
const handleWindowClick = (e: MouseEvent) => {
  // 只在点击el-card本身（即空白区域）时选中窗口，避免点击到子元素时误触发
  if (e.currentTarget === e.target) {
    designerStore.setSelectedComponent('__window__')
  }
}

// 处理组件选中（支持Ctrl/Shift多选与反选）
const handleSelectComponent = (componentId: string, event?: MouseEvent) => {
  if (isPreviewMode.value) return;
  
  // 选中组件时，取消窗口选中状态
  deselectWindow()
  
  // 获取当前多选列表
  const currentMulti = multiSelectedIds.value.slice();
  const compIndex = components.value.findIndex(c => c.id === componentId);
  
  // Ctrl/Cmd 多选增减
  if (event && (event.ctrlKey || event.metaKey)) {
    const idx = currentMulti.indexOf(componentId);
    if (idx > -1) {
      currentMulti.splice(idx, 1); // 取消选中
    } else {
      currentMulti.push(componentId); // 增加选中
    }
    multiSelectedIds.value = currentMulti;
    if (currentMulti.length === 1) {
      designerStore.setSelectedComponent(currentMulti[0]);
    } else {
      designerStore.setSelectedComponent(null);
    }
    return;
  }
  
  // Shift 区间多选
  if (event && event.shiftKey) {
    let lastId = null;
    if (currentMulti.length > 0) {
      lastId = currentMulti[currentMulti.length - 1];
    } else if (selectedComponentId.value) {
      lastId = selectedComponentId.value;
    }
    if (lastId && lastId !== componentId) {
      const lastIdx = components.value.findIndex(c => c.id === lastId);
      if (lastIdx !== -1 && compIndex !== -1) {
        const [start, end] = [lastIdx, compIndex].sort((a, b) => a - b);
        const rangeIds = components.value.slice(start, end + 1).map(c => c.id);
        // 合并去重
        const newMulti = Array.from(new Set([...currentMulti, ...rangeIds]));
        multiSelectedIds.value = newMulti;
        if (newMulti.length === 1) {
          designerStore.setSelectedComponent(newMulti[0]);
        } else {
          designerStore.setSelectedComponent(null);
        }
        return;
      }
    }
    // fallback: 只选当前
    multiSelectedIds.value = [componentId];
    designerStore.setSelectedComponent(componentId);
    return;
  }
  
  // 普通点击：单选
  multiSelectedIds.value = [componentId];
  designerStore.setSelectedComponent(componentId);
}

// 组件拖拽移动时，限制在window-container区域
const handleUpdateComponent = (componentId: string, updates: any) => {
  // 获取当前窗口的实际尺寸
  const maxW = windowConfig.value.width
  const maxH = windowConfig.value.height
  const comp = components.value.find(c => c.id === componentId)
  if (!comp) return
  
  // 获取组件的新位置和尺寸
  let x = updates.x !== undefined ? updates.x : comp.x
  let y = updates.y !== undefined ? updates.y : comp.y
  let width = updates.width !== undefined ? updates.width : comp.width
  let height = updates.height !== undefined ? updates.height : comp.height
  
  // 严格限制组件在窗口边界内
  // 确保组件不会超出窗口的右边界和下边界
  x = Math.max(0, Math.min(x, maxW - width))
  y = Math.max(0, Math.min(y, maxH - height))
  
  // 如果组件尺寸超过窗口，则调整尺寸
  if (width > maxW) {
    width = maxW
    x = 0
  }
  if (height > maxH) {
    height = maxH
    y = 0
  }
  
  // 更新组件位置和尺寸
  designerStore.updateComponent(componentId, {
    ...updates,
    x,
    y,
    width,
    height
  })
}

// 全局辅助线状态
const alignLinesMap = reactive(new Map<string, { type: 'vertical' | 'horizontal', position: number }[]>())
const allAlignLines = computed(() => {
  // 合并所有组件的辅助线，去重
  const lines: { type: 'vertical' | 'horizontal', position: number }[] = []
  alignLinesMap.forEach(arr => arr.forEach(l => {
    if (!lines.some(x => x.type === l.type && x.position === l.position)) lines.push(l)
  }))
  return lines
})

// 监听子组件辅助线事件
const handleAlignLines = (componentId: string, lines: { type: 'vertical' | 'horizontal', position: number }[]) => {
  if (!showAlignLines.value) return
  if (!lines || lines.length === 0) {
    alignLinesMap.delete(componentId)
  } else {
    alignLinesMap.set(componentId, lines)
  }
}

// 窗口选中状态
const isWindowSelected = ref(false)

// 选中窗口
const handleWindowSelect = () => {
  isWindowSelected.value = true
  // 选中窗口时，取消所有组件的选中状态
  designerStore.setSelectedComponent(null)
  multiSelectedIds.value = []
  // 清空辅助线
  clearAllAlignLines()
}

// 取消选中窗口
const deselectWindow = () => {
  isWindowSelected.value = false
}

// 窗口缩放方向
const resizeDirs = ['nw','n','ne','e','se','s','sw','w']

// 计算缩放控制点的位置 - 针对el-card结构优化
const getResizeHandleStyle = (dir: string) => {
  const baseStyle = {
    position: 'absolute' as const,
    zIndex: 20
  }
  
  // 获取窗口尺寸
  const width = windowConfig.value.width
  const height = windowConfig.value.height
  
  // 根据方向计算位置，考虑el-card的边框
  switch (dir) {
    case 'nw':
      return { ...baseStyle, left: '-6px', top: '-6px' }
    case 'n':
      return { ...baseStyle, left: `${width / 2 - 6}px`, top: '-6px' }
    case 'ne':
      return { ...baseStyle, left: `${width - 6}px`, top: '-6px' }
    case 'e':
      return { ...baseStyle, left: `${width - 6}px`, top: `${height / 2 - 6}px` }
    case 'se':
      return { ...baseStyle, left: `${width - 6}px`, top: `${height - 6}px` }
    case 's':
      return { ...baseStyle, left: `${width / 2 - 6}px`, top: `${height - 6}px` }
    case 'sw':
      return { ...baseStyle, left: '-6px', top: `${height - 6}px` }
    case 'w':
      return { ...baseStyle, left: '-6px', top: `${height / 2 - 6}px` }
    default:
      return baseStyle
  }
}

// 窗口缩放相关
const resizingWindow = ref(false)
const resizeWindowDir = ref('')
const resizeWindowStart = ref({ x: 0, y: 0, width: 800, height: 600 })

// 窗口缩放鼠标按下
const onWindowResizeMouseDown = (event: MouseEvent, dir: string) => {
  event.preventDefault()
  resizingWindow.value = true
  resizeWindowDir.value = dir
  resizeWindowStart.value = {
    x: event.clientX,
    y: event.clientY,
    width: windowConfig.value.width,
    height: windowConfig.value.height
  }
  window.addEventListener('mousemove', onWindowResizeMouseMove)
  window.addEventListener('mouseup', onWindowResizeMouseUp)
}

// 窗口缩放鼠标移动
const onWindowResizeMouseMove = (event: MouseEvent) => {
  if (!resizingWindow.value) return
  const dx = event.clientX - resizeWindowStart.value.x
  const dy = event.clientY - resizeWindowStart.value.y
  let newWidth = resizeWindowStart.value.width
  let newHeight = resizeWindowStart.value.height
  let newLeft = 0
  let newTop = 0
  switch (resizeWindowDir.value) {
    case 'e':
      newWidth = Math.max(200, resizeWindowStart.value.width + dx)
      break
    case 's':
      newHeight = Math.max(100, resizeWindowStart.value.height + dy)
      break
    case 'se':
      newWidth = Math.max(200, resizeWindowStart.value.width + dx)
      newHeight = Math.max(100, resizeWindowStart.value.height + dy)
      break
    case 'w':
      newWidth = Math.max(200, resizeWindowStart.value.width - dx)
      newLeft = Math.min(0, dx)
      break
    case 'n':
      newHeight = Math.max(100, resizeWindowStart.value.height - dy)
      newTop = Math.min(0, dy)
      break
    case 'nw':
      newWidth = Math.max(200, resizeWindowStart.value.width - dx)
      newLeft = Math.min(0, dx)
      newHeight = Math.max(100, resizeWindowStart.value.height - dy)
      newTop = Math.min(0, dy)
      break
    case 'ne':
      newWidth = Math.max(200, resizeWindowStart.value.width + dx)
      newHeight = Math.max(100, resizeWindowStart.value.height - dy)
      newTop = Math.min(0, dy)
      break
    case 'sw':
      newWidth = Math.max(200, resizeWindowStart.value.width - dx)
      newLeft = Math.min(0, dx)
      newHeight = Math.max(100, resizeWindowStart.value.height + dy)
      break
  }
  // 更新当前窗口的配置
  designerStore.updateWindowConfig(activeWindowId.value, {
    width: newWidth,
    height: newHeight
  })
}

// 窗口缩放鼠标抬起
const onWindowResizeMouseUp = () => {
  resizingWindow.value = false
  window.removeEventListener('mousemove', onWindowResizeMouseMove)
  window.removeEventListener('mouseup', onWindowResizeMouseUp)
  
  // 窗口缩放完成后，检查并调整所有组件位置
  // 确保没有组件超出新的窗口边界
  const newWidth = windowConfig.value.width
  const newHeight = windowConfig.value.height
  
  components.value.forEach(component => {
    let needsUpdate = false
    let updates: any = {}
    
    // 检查组件是否超出右边界
    if (component.x + component.width > newWidth) {
      updates.x = Math.max(0, newWidth - component.width)
      needsUpdate = true
    }
    
    // 检查组件是否超出下边界
    if (component.y + component.height > newHeight) {
      updates.y = Math.max(0, newHeight - component.height)
      needsUpdate = true
    }
    
    // 如果组件尺寸超过新窗口，则调整尺寸
    if (component.width > newWidth) {
      updates.width = newWidth
      updates.x = 0
      needsUpdate = true
    }
    
    if (component.height > newHeight) {
      updates.height = newHeight
      updates.y = 0
      needsUpdate = true
    }
    
    // 更新需要调整的组件
    if (needsUpdate) {
      designerStore.updateComponent(component.id, updates)
    }
  })
}

// 对齐操作方法（批量处理multiSelectedIds）
const alignLeft = () => {
  if (multiSelectedIds.value.length < 2) return;
  const selected = components.value.filter(c => multiSelectedIds.value.includes(c.id));
  const minX = Math.min(...selected.map(c => c.x));
  selected.forEach(c => designerStore.updateComponent(c.id, { x: minX }));
}
const alignCenterH = () => {
  if (multiSelectedIds.value.length < 2) return;
  const selected = components.value.filter(c => multiSelectedIds.value.includes(c.id));
  const centers = selected.map(c => c.x + c.width / 2);
  const avg = Math.round(centers.reduce((a, b) => a + b, 0) / centers.length);
  selected.forEach(c => designerStore.updateComponent(c.id, { x: avg - c.width / 2 }));
}
const alignRight = () => {
  if (multiSelectedIds.value.length < 2) return;
  const selected = components.value.filter(c => multiSelectedIds.value.includes(c.id));
  const maxR = Math.max(...selected.map(c => c.x + c.width));
  selected.forEach(c => designerStore.updateComponent(c.id, { x: maxR - c.width }));
}
const alignTop = () => {
  if (multiSelectedIds.value.length < 2) return;
  const selected = components.value.filter(c => multiSelectedIds.value.includes(c.id));
  const minY = Math.min(...selected.map(c => c.y));
  selected.forEach(c => designerStore.updateComponent(c.id, { y: minY }));
}
const alignCenterV = () => {
  if (multiSelectedIds.value.length < 2) return;
  const selected = components.value.filter(c => multiSelectedIds.value.includes(c.id));
  const centers = selected.map(c => c.y + c.height / 2);
  const avg = Math.round(centers.reduce((a, b) => a + b, 0) / centers.length);
  selected.forEach(c => designerStore.updateComponent(c.id, { y: avg - c.height / 2 }));
}
const alignBottom = () => {
  if (multiSelectedIds.value.length < 2) return;
  const selected = components.value.filter(c => multiSelectedIds.value.includes(c.id));
  const maxB = Math.max(...selected.map(c => c.y + c.height));
  selected.forEach(c => designerStore.updateComponent(c.id, { y: maxB - c.height }));
}
const alignWidth = () => {
  if (multiSelectedIds.value.length < 2) return;
  const selected = components.value.filter(c => multiSelectedIds.value.includes(c.id));
  const maxW = Math.max(...selected.map(c => c.width));
  selected.forEach(c => designerStore.updateComponent(c.id, { width: maxW }));
}
const alignHeight = () => {
  if (multiSelectedIds.value.length < 2) return;
  const selected = components.value.filter(c => multiSelectedIds.value.includes(c.id));
  const maxH = Math.max(...selected.map(c => c.height));
  selected.forEach(c => designerStore.updateComponent(c.id, { height: maxH }));
}
const distributeH = () => {
  if (multiSelectedIds.value.length < 3) return;
  const selected = components.value.filter(c => multiSelectedIds.value.includes(c.id)).sort((a, b) => a.x - b.x);
  const left = selected[0].x;
  const right = selected[selected.length - 1].x;
  const totalWidth = selected.reduce((sum, c) => sum + c.width, 0);
  const gap = (right + selected[selected.length - 1].width - left - totalWidth) / (selected.length - 1);
  let curX = left;
  selected.forEach((c, i) => {
    if (i === 0) {
      curX += c.width;
      return;
    }
    designerStore.updateComponent(c.id, { x: Math.round(curX + gap) });
    curX += c.width + gap;
  });
}
const distributeV = () => {
  if (multiSelectedIds.value.length < 3) return;
  const selected = components.value.filter(c => multiSelectedIds.value.includes(c.id)).sort((a, b) => a.y - b.y);
  const top = selected[0].y;
  const bottom = selected[selected.length - 1].y;
  const totalHeight = selected.reduce((sum, c) => sum + c.height, 0);
  const gap = (bottom + selected[selected.length - 1].height - top - totalHeight) / (selected.length - 1);
  let curY = top;
  selected.forEach((c, i) => {
    if (i === 0) {
      curY += c.height;
      return;
    }
    designerStore.updateComponent(c.id, { y: Math.round(curY + gap) });
    curY += c.height + gap;
  });
}

// 多选框选状态
const selectionBox = reactive({
  visible: false,
  startX: 0,
  startY: 0,
  endX: 0,
  endY: 0
})
import type { CSSProperties } from 'vue'

const selectionBoxStyle = computed<CSSProperties>(() => {
  if (!selectionBox.visible) return {}
  const x = Math.min(selectionBox.startX, selectionBox.endX)
  const y = Math.min(selectionBox.startY, selectionBox.endY)
  const w = Math.abs(selectionBox.endX - selectionBox.startX)
  const h = Math.abs(selectionBox.endY - selectionBox.startY)
  return {
    left: x + 'px',
    top: y + 'px',
    width: w + 'px',
    height: h + 'px',
    position: 'absolute',
    border: '1.5px dashed #409eff',
    background: 'rgba(64,158,255,0.08)',
    zIndex: 99,
    pointerEvents: 'none'
  }
})
// 框选事件处理
let isBoxSelecting = false
let boxStart = { x: 0, y: 0 }
// 替换为window-container内的框选事件
function handleWindowMouseDown(e: MouseEvent) {
  // 仅左键
  if (e.button !== 0) return
  
  // 获取点击的目标元素
  const target = e.target as HTMLElement
  
  // 检查是否点击的是缩放控制点
  if (target.classList.contains('resize-handle')) {
    // 点击缩放控制点时，选中窗口但不触发框选
    handleWindowSelect()
    return
  }
  
  // 检查是否点击的是组件
  if (target.classList.contains('canvas-component') || target.closest('.canvas-component')) {
    // 点击组件时，不选中窗口，让组件自己处理选中逻辑
    return
  }
  
  // 检查是否点击的是空白区域（不是组件）
  if (!target.classList.contains('canvas-component') && !target.closest('.canvas-component')) {
    // 点击窗口空白区域：选中窗口并触发框选
    console.log('框选开始 - 空窗口', { target: target.className, componentsCount: components.value.length })
    handleWindowSelect()
    isBoxSelecting = true
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    selectionBox.visible = true
    selectionBox.startX = x
    selectionBox.startY = y
    selectionBox.endX = x
    selectionBox.endY = y
    boxStart = { x, y }
  }
}
function onWindowMouseMove(e: MouseEvent) {
  if (!isBoxSelecting) return
  const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
  selectionBox.endX = e.clientX - rect.left
  selectionBox.endY = e.clientY - rect.top
}
function onWindowMouseUp(e: MouseEvent) {
  if (!isBoxSelecting) return
  isBoxSelecting = false
  selectionBox.visible = false
  
  // 计算选框范围
  const x1 = Math.min(selectionBox.startX, selectionBox.endX)
  const y1 = Math.min(selectionBox.startY, selectionBox.endY)
  const x2 = Math.max(selectionBox.startX, selectionBox.endX)
  const y2 = Math.max(selectionBox.startY, selectionBox.endY)
  
  // 计算与组件的碰撞（以window-container为基准）
  const selected: string[] = []
  for (const comp of components.value) {
    const cx1 = comp.x
    const cy1 = comp.y
    const cx2 = comp.x + comp.width
    const cy2 = comp.y + comp.height
    if (!(cx2 < x1 || cx1 > x2 || cy2 < y1 || cy1 > y2)) {
      selected.push(comp.id)
    }
  }
  
  // 更新选中状态
  multiSelectedIds.value = selected
  if (selected.length === 1) {
    // 单选：设置选中组件
    designerStore.setSelectedComponent(selected[0])
  } else if (selected.length > 1) {
    // 多选：不设置单个选中组件，但保持多选状态
    designerStore.setSelectedComponent(null)
  } else {
    // 没有选中任何组件：清空多选状态
    multiSelectedIds.value = []
    designerStore.setSelectedComponent(null)
  }
  // 如果框选没有选中任何组件，保持窗口选中状态
}
// 监听全局mouseup，防止鼠标移出画布丢失事件
function onGlobalMouseUp() {
  if (isBoxSelecting) {
    isBoxSelecting = false
    selectionBox.visible = false
    
    // 计算选框范围
    const x1 = Math.min(selectionBox.startX, selectionBox.endX)
    const y1 = Math.min(selectionBox.startY, selectionBox.endY)
    const x2 = Math.max(selectionBox.startX, selectionBox.endX)
    const y2 = Math.max(selectionBox.startY, selectionBox.endY)
    
    // 计算与组件的碰撞（以window-container为基准）
    const selected: string[] = []
    for (const comp of components.value) {
      const cx1 = comp.x
      const cy1 = comp.y
      const cx2 = comp.x + comp.width
      const cy2 = comp.y + comp.height
      if (!(cx2 < x1 || cx1 > x2 || cy2 < y1 || cy1 > y2)) {
        selected.push(comp.id)
      }
    }
    
    // 更新选中状态
    multiSelectedIds.value = selected
    if (selected.length === 1) {
      // 单选：设置选中组件
      designerStore.setSelectedComponent(selected[0])
    } else if (selected.length > 1) {
      // 多选：不设置单个选中组件，但保持多选状态
      designerStore.setSelectedComponent(null)
    } else {
      // 没有选中任何组件：清空多选状态
      multiSelectedIds.value = []
      designerStore.setSelectedComponent(null)
    }
  }
}
onMounted(() => {
  window.addEventListener('mouseup', onGlobalMouseUp)
})
onBeforeUnmount(() => {
  window.removeEventListener('mouseup', onGlobalMouseUp)
})

// 键盘批量操作：Delete批量删除、方向键批量移动、撤销重做
function handleKeyDown(e: KeyboardEvent) {
  if (document.activeElement && ['INPUT', 'TEXTAREA'].includes(document.activeElement.tagName)) return;
  
  // 撤销重做快捷键
  if (e.ctrlKey) {
    switch (e.key.toLowerCase()) {
      case 'z':
        e.preventDefault();
        if (e.shiftKey) {
          handleRedo();
        } else {
          handleUndo();
        }
        return;
      case 'y':
        e.preventDefault();
        handleRedo();
        return;
    }
  }
  
  // Delete 批量删除
  if ((e.key === 'Delete' || e.key === 'Backspace') && multiSelectedIds.value.length > 0) {
    multiSelectedIds.value.forEach(id => designerStore.removeComponent(id));
    multiSelectedIds.value = [];
    designerStore.setSelectedComponent(null);
    e.preventDefault();
  }
  // 方向键批量移动
  const moveStep = e.shiftKey ? 10 : 1;
  if (multiSelectedIds.value.length > 0 && ['ArrowUp','ArrowDown','ArrowLeft','ArrowRight'].includes(e.key)) {
    const dx = e.key === 'ArrowLeft' ? -moveStep : e.key === 'ArrowRight' ? moveStep : 0;
    const dy = e.key === 'ArrowUp' ? -moveStep : e.key === 'ArrowDown' ? moveStep : 0;
    
    multiSelectedIds.value.forEach(id => {
      const comp = components.value.find(c => c.id === id);
      if (!comp) return;
      
      // 计算新位置
      let newX = comp.x + dx;
      let newY = comp.y + dy;
      
      // 严格限制在窗口边界内
      newX = Math.max(0, Math.min(newX, windowConfig.value.width - comp.width));
      newY = Math.max(0, Math.min(newY, windowConfig.value.height - comp.height));
      
      // 更新组件位置
      designerStore.updateComponent(id, { x: newX, y: newY });
    });
    e.preventDefault();
  }
}
onMounted(() => {
  window.addEventListener('keydown', handleKeyDown);
  window.addEventListener('mouseup', onGlobalMouseUp)
})
onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleKeyDown);
  window.removeEventListener('mouseup', onGlobalMouseUp)
})

// 切换标签页 - 直接切换窗口，不改变路由
function handleTabClick(tab: any) {
  const windowId = tab.paneName
  const window = windows.value.find(w => w.id === windowId)
  if (window) {
    // 直接切换到对应窗口，保持单页面效果
    designerStore.setActiveWindow(windowId)
  }
}

// 新增窗口
function addWindow() {
  const newId = nanoid(6)
  const windowIndex = windows.value.length + 1
  const newName = `窗口${windowIndex}`
  const newRoute = `page${windowIndex}` // 使用新的路由命名规则

  // 添加新窗口到状态管理
  designerStore.addWindow({
    id: newId,
    name: newName,
    route: newRoute
  })

  // 直接切换到新窗口，不改变路由
  designerStore.setActiveWindow(newId)
}

// 关闭窗口
function removeWindow(targetName: string) {
  // 不能删除最后一个窗口
  if (windows.value.length <= 1) {
    ElMessage.warning('至少需要保留一个窗口')
    return
  }
  
  // 删除窗口
  designerStore.removeWindow(targetName)
  
  // 如果删除的是当前窗口，会自动切换到其他窗口
  // 路由会在状态管理的removeWindow中自动处理
}

// 标题栏事件处理函数
const handleTitleBarDarkToggle = () => {
  if (activeWindow.value) {
    const currentDarkMode = activeWindow.value.titleBarConfig?.isDarkMode ?? false
    designerStore.updateTitleBarConfig(activeWindow.value.id, {
      isDarkMode: !currentDarkMode
    })
    ElMessage.success(`已${!currentDarkMode ? '开启' : '关闭'}暗黑模式`)
  }
}

const handleTitleBarMinimize = () => {
  ElMessage.info('最小化功能演示')
}

const handleTitleBarMaximize = () => {
  ElMessage.info('最大化功能演示')
}

const handleTitleBarClose = () => {
  ElMessage.info('关闭功能演示')
}

// 处理body区域点击，选中窗口本身
function handleBodyWrapperClick(e: MouseEvent) {
  // 只在点击空白区域（不是组件、不是缩放点）时选中窗口
  const target = e.target as HTMLElement
  if (
    !target.closest('.canvas-component') && // 没点到组件
    !target.classList.contains('resize-handle') // 也不是缩放点
  ) {
    designerStore.setSelectedComponent('__window__')
  }
}

// 调试：监控windows变化，切换窗口类型时打印windows的完整内容
watch(windows, (val) => {
  console.log('windows changed:', JSON.parse(JSON.stringify(val)))
}, { deep: true })
</script>

<style scoped>
/* 画布容器 */
.canvas-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 画布工具栏 */
.canvas-toolbar {
  height: 48px;
  padding: 0 20px;
  background: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.toolbar-left, .toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-center {
  flex: 1;
  text-align: center;
}

.canvas-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.zoom-info {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  min-width: 50px;
  text-align: center;
  font-weight: 500;
}

/* 画布包装器 */
.canvas-wrapper {
  flex: 1;
  position: relative;
  background-color: #f5f7fa;
  overflow: auto;
}

/* 网格背景 - 根据showGrid状态动态显示 */
.canvas-wrapper.show-grid {
  background-image:
    linear-gradient(to right, rgba(0,0,0,0.07) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0,0,0,0.07) 1px, transparent 1px);
  background-size: 10px 10px;
}

/* 画布区域内的网格覆盖层（可选，保留原有实现） */
.grid-overlay {
  pointer-events: none;
}

/* 模拟窗口容器 - 使用Element Plus卡片组件 */
.window-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 800px;
  height: 600px;
  box-sizing: border-box;
  /* 关键修复：隐藏超出窗口边界的组件，防止组件显示在窗口外 */
  /* overflow: hidden; */
  /* 确保窗口容器有正确的层级，组件在其内部显示 */
  z-index: 2;
  transform: translateZ(0);
}



/* 窗口标题栏 */
.window-titlebar {
  height: 36px;
  background: linear-gradient(90deg, #e8eaec 0%, #f5f7fa 100%);
  border-bottom: 1px solid #d3dce6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  user-select: none;
}

.titlebar-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.window-icon {
  width: 16px;
  height: 16px;
  background: var(--el-color-primary);
  border-radius: 2px;
}

.window-title {
  font-weight: bold;
  color: #333;
  font-size: 15px;
}

.window-controls {
  display: flex;
  gap: 4px;
}

.control-btn {
  width: 22px;
  height: 22px;
  border: none;
  background: transparent;
  font-size: 15px;
  color: #666;
  border-radius: 3px;
  cursor: pointer;
  transition: background 0.2s;
}
.control-btn:hover {
  background: #e6e8eb;
}

/* 画布主体 - 模拟无标题栏窗口 */
.canvas {
  position: relative;
  background: #f8f9fa;
  cursor: crosshair;
  user-select: none;
}

/* 网格显示 */
.canvas.show-grid {
  background-image: 
    linear-gradient(to right, rgba(0,0,0,0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0,0,0,0.05) 1px, transparent 1px);
}

.grid-overlay {
  pointer-events: none;
  z-index: 1;
}

/* 画布信息栏 */
.canvas-info {
  height: 48px;
  padding: 0 20px;
  background: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.info-left, .info-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.selected-info {
  color: var(--el-color-primary);
  font-weight: 500;
}

.no-selection {
  color: var(--el-text-color-placeholder);
}

/* 窗口选中样式 - 针对el-card优化 */
.window-selected {
  box-shadow: 0 0 0 2px #409eff !important;
}

/* 确保el-card在选中时也应用相同样式 */
.window-selected.el-card {
  box-shadow: 0 0 0 2px #409eff, 0 8px 32px rgba(0,0,0,0.18) !important;
}

/* 窗口容器点击样式 */
.window-container {
  cursor: default;
  transition: box-shadow 0.2s ease;
}

.window-container:hover {
  box-shadow: 0 0 0 1px #409eff, 0 8px 32px rgba(0,0,0,0.18) !important;
}

/* 8点缩放控制点样式 - 针对el-card优化 */
.resize-handle {
  width: 12px;
  height: 12px;
  background: #409eff;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 1px 4px rgba(0,0,0,0.08);
  /* 位置由getResizeHandleStyle动态计算，移除静态定位 */
}

/* 窗口缩放控制点 - 专门针对窗口容器的控制点 */
.window-resize-handle {
  /* 确保控制点在el-card之上 */
  z-index: 25 !important;
}

/* 鼠标样式 - 根据方向设置不同的光标 */
.resize-nw { cursor: nwse-resize; }
.resize-n { cursor: ns-resize; }
.resize-ne { cursor: nesw-resize; }
.resize-e { cursor: ew-resize; }
.resize-se { cursor: nwse-resize; }
.resize-s { cursor: ns-resize; }
.resize-sw { cursor: nesw-resize; }
.resize-w { cursor: ew-resize; }

/* 辅助线样式（可放在<style>里） */
.align-line {
  position: absolute;
  z-index: 100;
  background: #ff4d4f;
  pointer-events: none;
}
.align-line.vertical {
  top: 0;
  bottom: 0;
  width: 1px;
  height: 100%;
}
.align-line.horizontal {
  left: 0;
  right: 0;
  height: 1px;
  width: 100%;
}

/* 滚动条样式 */
.canvas-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.canvas-wrapper::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
}

.canvas-wrapper::-webkit-scrollbar-thumb {
  background: var(--el-border-color-dark);
  border-radius: 4px;
}

.canvas-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-darker);
}



/* 框选样式 */
.selection-box {
  pointer-events: none;
  border: 1.5px dashed #409eff;
  background: rgba(64,158,255,0.08);
  position: absolute;
  z-index: 99;
}

/* 卡片体包装器样式 */
.card-body-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 空状态容器样式 - 确保不影响框选 */
.empty-state-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none; /* 关键：不阻止鼠标事件 */
  z-index: 1;
}

/* 确保空状态容器内的所有元素都不阻止事件 */
.empty-state-container :deep(*) {
  pointer-events: none;
}

/* 窗口切换栏容器.el-tabs__new-tab */
.window-tabs-container {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.window-tabs-container :deep(.el-tabs__new-tab) {
  margin: 10px;
}

/* 集成标题栏样式 - 与el-card完美融合 */
.integrated-title-bar {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
  padding: 8px 20px;
  height: 36px;
  min-height: 36px;
  background: transparent;
  user-select: none;
  transition: all 0.3s ease;
  border-bottom: none;
  position: relative;
  z-index: 1;
}

/* 暗黑模式样式 */
.integrated-title-bar.dark-mode {
  background: linear-gradient(90deg, #2c2c2c 0%, #3a3a3a 100%);
  border-bottom-color: #4a4a4a;
  color: #ffffff;
}

/* 左侧区域 */
.title-bar-left {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
}

/* 应用LOGO容器 */
.title-bar-left .app-logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 3px;
  overflow: hidden;
  background: var(--el-fill-color-lighter);
  border: 1px solid var(--el-border-color-light);
}

.title-bar-left .app-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 默认图标 */
.title-bar-left .default-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 3px;
  background: var(--el-color-primary);
  color: white;
}

/* 窗口标题 */
.title-bar-left .window-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.integrated-title-bar.dark-mode .window-title {
  color: #ffffff;
}

/* 右侧控制按钮区域 */
.title-bar-controls {
  display: flex;
  align-items: center;
  gap: 2px;
}

/* 控制按钮基础样式 */
.title-control-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: var(--el-text-color-secondary);
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.title-control-btn:hover {
  background: var(--el-fill-color-light);
  transform: scale(1.05);
}

.integrated-title-bar.dark-mode .title-control-btn {
  color: #cccccc;
}

.integrated-title-bar.dark-mode .title-control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 暗黑模式切换按钮 */
.dark-toggle-btn {
  color: var(--el-color-warning) !important;
}

.dark-toggle-btn.active {
  background: var(--el-color-warning-light-9) !important;
  color: var(--el-color-warning) !important;
}

.dark-toggle-btn:hover {
  background: var(--el-color-warning-light-8) !important;
  color: var(--el-color-warning-dark-2) !important;
}

/* 最小化按钮 */
.minimize-btn:hover {
  background: var(--el-color-info-light-8) !important;
  color: var(--el-color-info) !important;
}

/* 最大化按钮 */
.maximize-btn:hover {
  background: var(--el-color-success-light-8) !important;
  color: var(--el-color-success) !important;
}

/* 关闭按钮 */
.close-btn:hover {
  background: var(--el-color-danger) !important;
  color: #ffffff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .integrated-title-bar {
    padding: 0 12px;
  }

  .title-bar-left {
    gap: 6px;
  }

  .title-bar-left .window-title {
    font-size: 13px;
  }

  .title-control-btn {
    width: 20px;
    height: 20px;
  }
}

/* 模拟窗口独立暗黑模式样式 - 覆盖设计器暗黑模式 */
.window-container {
  /* 强制覆盖设计器暗黑模式，保持模拟窗口的独立性 */
  background: #ffffff !important;
  border-color: #ebeef5 !important;
  color: #303133 !important;
}

.window-container :deep(.el-card__header) {
  background: #ffffff !important;
  border-bottom: 1px solid #ebeef5 !important; /* 保持标题栏下方的分隔线 */
  color: #303133 !important;
  padding: 0 !important; /* 移除默认padding，让integrated-title-bar完全控制 */
  display: block !important;
  min-height: 36px !important;
  height: auto !important;
}

.window-container :deep(.el-card__body) {
  background: #ffffff !important;
  color: #303133 !important;
}

.card-body-wrapper {
  background: #ffffff !important;
  color: #303133 !important;
}

/* 模拟窗口暗黑模式 - 仅当窗口自身开启暗黑模式时生效 */
.window-container.window-dark-mode {
  background: #1f1f1f !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.window-container.window-dark-mode :deep(.el-card__header) {
  background: #2c2c2c !important;
  border-bottom: 1px solid #404040 !important; /* 暗黑模式下的分隔线 */
  color: #ffffff !important;
  padding: 0 !important; /* 移除默认padding */
  display: block !important;
  min-height: 36px !important;
  height: auto !important;
}

.window-container.window-dark-mode :deep(.el-card__body) {
  background: #1f1f1f !important;
  color: #ffffff !important;
}

.card-body-wrapper.window-dark-mode {
  background: #1f1f1f !important;
  color: #ffffff !important;
}

/* 模拟窗口内组件的默认样式 - 强制覆盖设计器暗黑模式 */
.canvas-component :deep(.el-input) {
  --el-input-bg-color: #ffffff !important;
  --el-input-border-color: #dcdfe6 !important;
  --el-input-text-color: #606266 !important;
  --el-input-placeholder-color: #a8abb2 !important;
}

.canvas-component :deep(.el-input__wrapper) {
  background-color: #ffffff !important;
  border-color: #dcdfe6 !important;
  color: #606266 !important;
}

.canvas-component :deep(.el-input__inner) {
  color: #606266 !important;
  background-color: transparent !important;
}

.canvas-component :deep(.el-button) {
  --el-button-bg-color: #ffffff !important;
  --el-button-border-color: #dcdfe6 !important;
  --el-button-text-color: #606266 !important;
}

.canvas-component :deep(.el-button--primary) {
  --el-button-bg-color: #409eff !important;
  --el-button-border-color: #409eff !important;
  --el-button-text-color: #ffffff !important;
}

.canvas-component :deep(.el-text) {
  color: #606266 !important;
}

.canvas-component :deep(.el-select) {
  --el-select-input-color: #606266 !important;
  --el-select-input-focus-border-color: #409eff !important;
}

.canvas-component :deep(.el-select .el-input__wrapper) {
  background-color: #ffffff !important;
  border-color: #dcdfe6 !important;
}

.canvas-component :deep(.el-textarea) {
  --el-input-bg-color: #ffffff !important;
  --el-input-border-color: #dcdfe6 !important;
  --el-input-text-color: #606266 !important;
}

.canvas-component :deep(.el-textarea__inner) {
  background-color: #ffffff !important;
  border-color: #dcdfe6 !important;
  color: #606266 !important;
}

/* 模拟窗口内组件的暗黑模式样式 - 仅当窗口自身开启暗黑模式时生效 */
.canvas-component.window-dark-mode :deep(.el-input) {
  --el-input-bg-color: #2c2c2c !important;
  --el-input-border-color: #404040 !important;
  --el-input-text-color: #ffffff !important;
  --el-input-placeholder-color: #8c8c8c !important;
}

.canvas-component.window-dark-mode :deep(.el-input__wrapper) {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.canvas-component.window-dark-mode :deep(.el-input__inner) {
  color: #ffffff !important;
  background-color: transparent !important;
}

.canvas-component.window-dark-mode :deep(.el-button) {
  --el-button-bg-color: #404040 !important;
  --el-button-border-color: #606060 !important;
  --el-button-text-color: #ffffff !important;
}

.canvas-component.window-dark-mode :deep(.el-button--primary) {
  --el-button-bg-color: #409eff !important;
  --el-button-border-color: #409eff !important;
  --el-button-text-color: #ffffff !important;
}

.canvas-component.window-dark-mode :deep(.el-text) {
  color: #ffffff !important;
}

.canvas-component.window-dark-mode :deep(.el-select) {
  --el-select-input-color: #ffffff !important;
  --el-select-input-focus-border-color: #409eff !important;
}

.canvas-component.window-dark-mode :deep(.el-select .el-input__wrapper) {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
}

.canvas-component.window-dark-mode :deep(.el-textarea) {
  --el-input-bg-color: #2c2c2c !important;
  --el-input-border-color: #404040 !important;
  --el-input-text-color: #ffffff !important;
}

.canvas-component.window-dark-mode :deep(.el-textarea__inner) {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

</style>