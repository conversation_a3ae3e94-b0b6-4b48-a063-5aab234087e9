import JSZip from 'jszip'
import { saveAs } from 'file-saver'
import type { WindowData, ComponentData } from '@/stores/designer'

// 导出项目为完整的Vue3+ElementPlus项目
export async function exportProject(windows: WindowData[], projectName: string = 'easy-window-project') {
  const zip = new JSZip()

  // 创建项目基础结构
  await createProjectStructure(zip, windows, projectName)

  // 生成ZIP文件并下载
  const content = await zip.generateAsync({ type: 'blob' })
  saveAs(content, `${projectName}.zip`)
}

// 创建项目结构
async function createProjectStructure(zip: JSZip, windows: WindowData[], projectName: string) {
  // 1. 创建package.json
  zip.file('package.json', generatePackageJson(projectName))

  // 2. 创建vite.config.ts
  zip.file('vite.config.ts', generateViteConfig())

  // 3. 创建tsconfig.json
  zip.file('tsconfig.json', generateTsConfig())

  // 3.1 创建tsconfig.node.json
  zip.file('tsconfig.node.json', generateTsNodeConfig())

  // 4. 创建index.html
  zip.file('index.html', generateIndexHtml(projectName))

  // 5. 创建src目录结构
  const srcFolder = zip.folder('src')!
  
  // 创建main.ts
  srcFolder.file('main.ts', generateMainTs())

  // 创建App.vue
  srcFolder.file('App.vue', generateAppVue())

  // 创建路由配置
  const routerFolder = srcFolder.folder('router')!
  routerFolder.file('index.ts', generateRouterConfig(windows))

  // 创建窗口组件
  const viewsFolder = srcFolder.folder('views')!
  for (const window of windows) {
    viewsFolder.file(`${window.name}.vue`, generateWindowComponent(window))
  }

  // 创建组件文件夹
  const componentsFolder = srcFolder.folder('components')!
  componentsFolder.file('WindowTitleBar.vue', generateWindowTitleBarComponent())

  // 创建样式文件
  srcFolder.file('style.css', generateGlobalStyles())

  // 6. 创建public目录
  const publicFolder = zip.folder('public')!
  
  // 复制LOGO文件（如果有的话）
  for (const window of windows) {
    if (window.titleBarConfig?.logoPath && window.titleBarConfig.logoPath.startsWith('/logos/')) {
      // 这里可以添加LOGO文件的复制逻辑
    }
  }

  // 7. 创建README.md
  zip.file('README.md', generateReadme(projectName))

  // 8. 创建.gitignore
  zip.file('.gitignore', generateGitignore())
}

// 生成package.json
function generatePackageJson(projectName: string): string {
  return JSON.stringify({
    name: projectName.toLowerCase().replace(/\s+/g, '-'),
    private: true,
    version: '0.0.0',
    type: 'module',
    scripts: {
      dev: 'vite',
      build: 'vue-tsc && vite build',
      preview: 'vite preview'
    },
    dependencies: {
      vue: '^3.4.0',
      'vue-router': '^4.2.5',
      'element-plus': '^2.4.4',
      '@element-plus/icons-vue': '^2.3.1'
    },
    devDependencies: {
      '@types/node': '^20.10.0',
      '@vitejs/plugin-vue': '^4.5.2',
      typescript: '^5.2.2',
      'vue-tsc': '^1.8.25',
      vite: '^5.0.8',
      terser: '^5.24.0'
    }
  }, null, 2)
}

// 生成vite.config.ts
function generateViteConfig(): string {
  return `import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    open: true
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router'],
          elementPlus: ['element-plus', '@element-plus/icons-vue']
        }
      }
    }
  }
})
`
}

// 生成tsconfig.json
function generateTsConfig(): string {
  return JSON.stringify({
    compilerOptions: {
      target: 'ES2020',
      useDefineForClassFields: true,
      lib: ['ES2020', 'DOM', 'DOM.Iterable'],
      module: 'ESNext',
      skipLibCheck: true,
      moduleResolution: 'bundler',
      allowImportingTsExtensions: true,
      resolveJsonModule: true,
      isolatedModules: true,
      noEmit: true,
      jsx: 'preserve',
      strict: true,
      noUnusedLocals: false,
      noUnusedParameters: false,
      noFallthroughCasesInSwitch: true,
      baseUrl: '.',
      paths: {
        '@/*': ['src/*']
      }
    },
    include: ['src/**/*.ts', 'src/**/*.d.ts', 'src/**/*.tsx', 'src/**/*.vue'],
    references: [{ path: './tsconfig.node.json' }]
  }, null, 2)
}

// 生成tsconfig.node.json
function generateTsNodeConfig(): string {
  return JSON.stringify({
    compilerOptions: {
      composite: true,
      skipLibCheck: true,
      module: 'ESNext',
      moduleResolution: 'bundler',
      allowSyntheticDefaultImports: true
    },
    include: ['vite.config.ts']
  }, null, 2)
}

// 生成index.html
function generateIndexHtml(projectName: string): string {
  return `<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>${projectName}</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
`
}

// 生成main.ts
function generateMainTs(): string {
  return `import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import router from './router'
import App from './App.vue'
import './style.css'

const app = createApp(App)

// 注册Element Plus
app.use(ElementPlus)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册路由
app.use(router)

app.mount('#app')
`
}

// 生成App.vue
function generateAppVue(): string {
  return `<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
// 应用根组件
</script>

<style>
#app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}
</style>
`
}

// 生成全局样式
function generateGlobalStyles(): string {
  return `/* 全局样式重置 */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  overflow: hidden;
}

* {
  box-sizing: border-box;
}

#app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* 防止组件触发原生拖拽行为 */
* {
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
}

/* 允许输入框文本选择 */
.el-input__inner,
.el-textarea__inner {
  user-select: text !important;
}

/* 组件基础样式 */
.component-item {
  position: absolute;
  transition: all 0.2s ease;
}

/* 暗黑模式支持 */
.dark-mode {
  background: #1a1a1a;
  color: #ffffff;
}
`
}

// 生成README.md
function generateReadme(projectName: string): string {
  return `# ${projectName}

这是一个由 Easy Window 可视化设计器生成的 Vue3 + Element Plus 项目。

## 项目结构

\`\`\`
${projectName}/
├── src/
│   ├── views/          # 窗口组件
│   ├── components/     # 公共组件
│   ├── router/         # 路由配置
│   ├── App.vue         # 根组件
│   ├── main.ts         # 入口文件
│   └── style.css       # 全局样式
├── public/             # 静态资源
├── package.json        # 项目配置
├── vite.config.ts      # Vite 配置
├── tsconfig.json       # TypeScript 配置
└── README.md           # 项目说明
\`\`\`

## 开发

\`\`\`bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
\`\`\`

## 技术栈

- Vue 3
- TypeScript
- Element Plus
- Vue Router
- Vite

## 说明

此项目由 Easy Window 可视化设计器自动生成，包含完整的项目结构和配置文件。
您可以直接运行 \`npm install\` 和 \`npm run dev\` 来启动项目。
`
}

// 生成.gitignore
function generateGitignore(): string {
  return `# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
`
}

// 生成路由配置
function generateRouterConfig(windows: WindowData[]): string {
  const routes = windows.map(window => {
    return `  {
    path: '/${window.id}',
    name: '${window.name}',
    component: () => import('@/views/${window.name}.vue'),
    meta: {
      title: '${window.titleBarConfig?.title || window.name}'
    }
  }`
  }).join(',\n')

  return `import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/${windows[0]?.id || 'main'}'
  },
${routes}
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }
  next()
})

export default router
`
}

// 生成窗口组件
function generateWindowComponent(window: WindowData): string {
  const components = window.components.map(comp => generateComponentCode(comp)).join('\n    ')

  const titleBarHtml = window.windowType === 'normal' ? `
        <!-- 标题栏 -->
        <WindowTitleBar
          :title="'${window.titleBarConfig?.title || window.name}'"
          :show-logo="${window.titleBarConfig?.showLogo || false}"
          :logo-path="'${window.titleBarConfig?.logoPath || ''}'"
          :show-dark-toggle="${window.titleBarConfig?.showDarkToggle || false}"
          :show-minimize="${window.titleBarConfig?.showMinimize || false}"
          :show-maximize="${window.titleBarConfig?.showMaximize || false}"
          :show-close="${window.titleBarConfig?.showClose || false}"
          :is-dark-mode="isDarkMode"
          @dark-toggle="toggleDarkMode"
          @minimize="minimize"
          @maximize="maximize"
          @close="closeWindow"
        />` : ''

  return `<template>
  <div class="preview-window" :class="{ 'window-dark-mode': isDarkMode }">
    <div class="window-container" :style="windowStyle">
      ${titleBarHtml}

      <!-- 窗口内容区域 -->
      <div class="window-content" :style="contentStyle">
        ${components}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import WindowTitleBar from '@/components/WindowTitleBar.vue'

// 响应式数据
const isDarkMode = ref(${window.titleBarConfig?.isDarkMode || false})

// 组件数据
const inputValue = ref('')
const selectValue = ref('')
const textareaValue = ref('')
const checkboxValue = ref(false)
const radioValue = ref('')
const switchValue = ref(false)
const sliderValue = ref(0)
const dateValue = ref(null)
const timeValue = ref(null)
const rateValue = ref(0)
const colorValue = ref('#409EFF')

// 计算属性 - 完全复制预览窗口的样式计算
const windowStyle = computed(() => ({
  width: '${window.windowConfig.width}px',
  height: '${window.windowConfig.height}px',
  backgroundColor: '${window.windowConfig.backgroundColor}',
  border: '${window.windowConfig.border}',
  borderRadius: '${window.windowConfig.borderRadius}px',
  boxShadow: '${window.windowConfig.boxShadow}',
  position: 'relative',
  overflow: 'hidden'
}))

const contentStyle = computed(() => ({
  width: '100%',
  height: '${window.windowType === 'normal' ? `calc(100% - ${window.titleBarConfig?.height || 36}px)` : '100%'}',
  position: 'relative',
  backgroundColor: 'transparent'
}))

// 方法
const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value
  ElMessage.success(\`已切换到\${isDarkMode.value ? '暗黑' : '亮色'}模式\`)
}

const minimize = () => {
  ElMessage.info('最小化功能')
}

const maximize = () => {
  ElMessage.info('最大化功能')
}

const closeWindow = () => {
  ElMessage.info('关闭窗口功能')
}

const handleButtonClick = () => {
  ElMessage.success('按钮被点击')
}
</script>

<style scoped>
/* 完全复制预览窗口的样式 */
.preview-window {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  transition: all 0.3s ease;
  overflow: hidden;
}

.preview-window.window-dark-mode {
  background: #1a1a1a;
}

.window-container {
  position: relative;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.window-content {
  position: relative;
  background: transparent;
}

/* 完全复制预览组件的样式 */
.preview-component {
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.component-inner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 统一 input 拉伸样式 */
.component-inner :deep(.el-input),
.component-inner :deep(.el-input__wrapper),
.component-inner :deep(.el-input__inner) {
  width: 100% !important;
  height: 100% !important;
  min-width: 0 !important;
  min-height: 0 !important;
  box-sizing: border-box !important;
  padding: 0 !important;
}

/* 图片组件样式 */
.component-inner :deep(.el-image) {
  width: 100% !important;
  height: 100% !important;
  min-height: 0 !important;
  max-height: none !important;
  display: block !important;
  flex: none !important;
  position: relative !important;
}

.component-inner :deep(.el-image__inner) {
  width: 100% !important;
  height: 100% !important;
  min-height: 0 !important;
  max-height: none !important;
  object-fit: inherit !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
}

/* 图片组件容器特殊处理 */
.component-inner.image-container {
  display: block !important;
  position: relative !important;
}

/* 暗黑模式下的组件样式 */
.preview-component.window-dark-mode .component-inner :deep(.el-input) {
  --el-input-bg-color: #2c2c2c !important;
  --el-input-border-color: #404040 !important;
  --el-input-text-color: #ffffff !important;
  --el-input-placeholder-color: #8c8c8c !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-input__wrapper) {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-input__inner) {
  color: #ffffff !important;
  background-color: transparent !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-button) {
  --el-button-bg-color: #404040 !important;
  --el-button-border-color: #606060 !important;
  --el-button-text-color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-button--primary) {
  --el-button-bg-color: #409eff !important;
  --el-button-border-color: #409eff !important;
  --el-button-text-color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-select) {
  --el-select-input-color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-select .el-input__wrapper) {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-textarea) {
  --el-input-bg-color: #2c2c2c !important;
  --el-input-border-color: #404040 !important;
  --el-input-text-color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-textarea__inner) {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner span {
  color: #ffffff !important;
}

/* 日期时间选择器暗黑模式 */
.preview-component.window-dark-mode .component-inner :deep(.el-date-editor) {
  --el-input-bg-color: #2c2c2c !important;
  --el-input-border-color: #404040 !important;
  --el-input-text-color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-date-editor .el-input__wrapper) {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-date-editor .el-input__inner) {
  color: #ffffff !important;
  background-color: transparent !important;
}

/* 评分组件暗黑模式 */
.preview-component.window-dark-mode .component-inner :deep(.el-rate) {
  --el-rate-void-color: #404040 !important;
  --el-rate-fill-color: #F7BA2A !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-rate__text) {
  color: #ffffff !important;
}

/* 颜色选择器暗黑模式 */
.preview-component.window-dark-mode .component-inner :deep(.el-color-picker) {
  --el-color-picker-border-color: #404040 !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-color-picker__trigger) {
  border-color: #404040 !important;
}

/* 滑块组件暗黑模式 */
.preview-component.window-dark-mode .component-inner :deep(.el-slider) {
  --el-slider-runway-bg-color: #404040 !important;
  --el-slider-main-bg-color: #409eff !important;
  --el-slider-button-bg-color: #409eff !important;
}

/* 开关组件暗黑模式 */
.preview-component.window-dark-mode .component-inner :deep(.el-switch) {
  --el-switch-off-color: #404040 !important;
  --el-switch-on-color: #409eff !important;
}

/* 复选框和单选框暗黑模式 */
.preview-component.window-dark-mode .component-inner :deep(.el-checkbox) {
  --el-checkbox-bg-color: #2c2c2c !important;
  --el-checkbox-border-color: #404040 !important;
  color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-radio) {
  --el-radio-bg-color: #2c2c2c !important;
  --el-radio-border-color: #404040 !important;
  color: #ffffff !important;
}

.preview-component.window-dark-mode .component-inner :deep(.el-checkbox__label),
.preview-component.window-dark-mode .component-inner :deep(.el-radio__label) {
  color: #ffffff !important;
}

.window-title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 20px;
  height: ${window.titleBarConfig?.height || 36}px;
  background: transparent;
  user-select: none;
}

.window-title-bar.dark-mode {
  background: linear-gradient(90deg, #2c2c2c 0%, #3a3a3a 100%);
  color: #ffffff;
}

.title-bar-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.app-logo {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.window-title {
  font-weight: 600;
  font-size: 14px;
}

.title-bar-controls {
  display: flex;
  gap: 2px;
}

.control-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: var(--el-fill-color-light);
}

.close-btn:hover {
  background: var(--el-color-danger);
  color: #ffffff;
}

.window-content {
  position: relative;
  width: 100%;
  height: 100%;
  background: #ffffff;
  transition: all 0.3s ease;
}

.window-content.window-dark-mode {
  background: #1f1f1f;
  color: #ffffff;
}

/* 组件样式 */
.component-item {
  position: absolute;
  transition: all 0.2s ease;
}

/* 输入框组件样式 */
.component-item.el-input,
.component-item .el-input,
.component-item .el-input__wrapper,
.component-item .el-input__inner {
  width: 100% !important;
  height: 100% !important;
  min-width: 0 !important;
  min-height: 0 !important;
  box-sizing: border-box !important;
}

/* 按钮组件样式 */
.component-item.el-button {
  width: 100% !important;
  height: 100% !important;
}

/* 图片组件样式 */
.component-item.el-image {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
}

/* 选择器组件样式 */
.component-item.el-select {
  width: 100% !important;
}

.component-item .el-select .el-input__wrapper {
  width: 100% !important;
  height: 100% !important;
}

/* 文本域组件样式 */
.component-item .el-textarea {
  width: 100% !important;
  height: 100% !important;
}

.component-item .el-textarea__inner {
  width: 100% !important;
  height: 100% !important;
  resize: none !important;
}

/* 暗黑模式下的组件样式 */
.window-content.window-dark-mode .component-item .el-input {
  --el-input-bg-color: #2c2c2c !important;
  --el-input-border-color: #404040 !important;
  --el-input-text-color: #ffffff !important;
  --el-input-placeholder-color: #8c8c8c !important;
}

.window-content.window-dark-mode .component-item .el-input__wrapper {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.window-content.window-dark-mode .component-item .el-input__inner {
  color: #ffffff !important;
  background-color: transparent !important;
}

.window-content.window-dark-mode .component-item .el-button {
  --el-button-bg-color: #404040 !important;
  --el-button-border-color: #606060 !important;
  --el-button-text-color: #ffffff !important;
}

.window-content.window-dark-mode .component-item .el-button--primary {
  --el-button-bg-color: #409eff !important;
  --el-button-border-color: #409eff !important;
  --el-button-text-color: #ffffff !important;
}

.window-content.window-dark-mode .component-item .el-select {
  --el-select-input-color: #ffffff !important;
}

.window-content.window-dark-mode .component-item .el-select .el-input__wrapper {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
}

.window-content.window-dark-mode .component-item .el-textarea {
  --el-input-bg-color: #2c2c2c !important;
  --el-input-border-color: #404040 !important;
  --el-input-text-color: #ffffff !important;
}

.window-content.window-dark-mode .component-item .el-textarea__inner {
  background-color: #2c2c2c !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.window-content.window-dark-mode .component-item span {
  color: #ffffff !important;
}
</style>
`
}

// 生成单个组件代码 - 完全复制预览组件的结构
function generateComponentCode(component: ComponentData): string {
  const componentStyle = `
    position: absolute;
    left: ${component.x}px;
    top: ${component.y}px;
    width: ${component.width}px;
    height: ${component.height}px;
  `

  return `<div
      class="preview-component"
      :class="{ 'window-dark-mode': isDarkMode }"
      style="${componentStyle}"
    >
      <div class="component-inner" :class="{ 'image-container': '${component.type}' === 'image' }">
        ${generateInnerComponent(component)}
      </div>
    </div>`
}

// 生成组件内部内容
function generateInnerComponent(component: ComponentData): string {
  switch (component.type) {
    case 'input':
      return `<el-input
        v-model="inputValue"
        type="${component.props?.inputType || 'text'}"
        placeholder="${component.props?.placeholder || '请输入内容'}"
        :disabled="${component.props?.disabled || false}"
        :readonly="${component.props?.readonly || false}"
        :clearable="${component.props?.clearable || false}"
        :show-password="${component.props?.showPassword || false}"
        :show-word-limit="${component.props?.showWordLimit || false}"
        :maxlength="${component.props?.maxlength || undefined}"
        :minlength="${component.props?.minlength || undefined}"
        size="${component.props?.size || 'default'}"
        autocomplete="${component.props?.autocomplete || 'off'}"
      />`

    case 'button':
      return `<el-button
        style="width:100%;height:100%;"
        type="${component.props?.type || 'primary'}"
        size="${component.props?.size || 'default'}"
        :disabled="${component.props?.disabled || false}"
        :plain="${component.props?.plain || false}"
        :round="${component.props?.round || false}"
        :circle="${component.props?.circle || false}"
        :loading="${component.props?.loading || false}"
        :autofocus="${component.props?.autofocus || false}"
        native-type="${component.props?.nativeType || 'button'}"
        @click="handleButtonClick"
      >
        ${component.props?.text || '按钮'}
      </el-button>`

    case 'text':
      return `<el-text
        type="${component.props?.type || 'primary'}"
        size="${component.props?.size || 'default'}"
        :truncated="${component.props?.truncated || false}"
        tag="${component.props?.tag || 'span'}"
        style="width:100%;height:100%;display:flex;align-items:center;justify-content:center;"
      >
        ${component.props?.content || '文本内容'}
      </el-text>`

    case 'image':
      return `<el-image
        src="${component.props?.src || 'https://via.placeholder.com/150'}"
        alt="${component.props?.alt || '图片'}"
        fit="${component.props?.fit || 'cover'}"
        loading="${component.props?.loading || 'eager'}"
        :lazy="${component.props?.lazy || false}"
        draggable="false"
        style="width: 100%; height: 100%; display: block;"
      />`

    case 'select':
      const options = component.props?.options || []
      const optionElements = options.map((opt: any) =>
        `<el-option label="${opt.label}" value="${opt.value}" ${opt.disabled ? ':disabled="true"' : ''} />`
      ).join('\n        ')

      return `<el-select
        v-model="selectValue"
        placeholder="${component.props?.placeholder || '请选择'}"
        :disabled="${component.props?.disabled || false}"
        :clearable="${component.props?.clearable || false}"
        size="${component.props?.size || 'default'}"
        style="width:100%;"
      >
        ${optionElements}
      </el-select>`

    case 'textarea':
      return `<el-input
        v-model="textareaValue"
        type="textarea"
        placeholder="${component.props?.placeholder || '请输入内容'}"
        :rows="${component.props?.rows || 3}"
        :disabled="${component.props?.disabled || false}"
        style="width:100%;height:100%;"
      />`

    case 'checkbox':
      return `<el-checkbox
        v-model="checkboxValue"
        :disabled="${component.props?.disabled || false}"
        size="${component.props?.size || 'default'}"
      >
        ${component.props?.label || '复选框'}
      </el-checkbox>`

    case 'radio':
      return `<el-radio
        v-model="radioValue"
        label="${component.props?.value || 'option1'}"
        :disabled="${component.props?.disabled || false}"
        size="${component.props?.size || 'default'}"
      >
        ${component.props?.label || '单选框'}
      </el-radio>`

    case 'switch':
      return `<el-switch
        v-model="switchValue"
        :disabled="${component.props?.disabled || false}"
        size="${component.props?.size || 'default'}"
        active-text="${component.props?.activeText || ''}"
        inactive-text="${component.props?.inactiveText || ''}"
      />`

    case 'slider':
      return `<el-slider
        v-model="sliderValue"
        :min="${component.props?.min || 0}"
        :max="${component.props?.max || 100}"
        :step="${component.props?.step || 1}"
        :disabled="${component.props?.disabled || false}"
        :show-input="${component.props?.showInput || false}"
        style="width:100%;"
      />`

    case 'date-picker':
      return `<el-date-picker
        v-model="dateValue"
        type="${component.props?.type || 'date'}"
        placeholder="${component.props?.placeholder || '选择日期'}"
        :disabled="${component.props?.disabled || false}"
        :clearable="${component.props?.clearable || true}"
        :readonly="${component.props?.readonly || false}"
        size="${component.props?.size || 'default'}"
        format="${component.props?.format || 'YYYY-MM-DD'}"
        value-format="${component.props?.valueFormat || 'YYYY-MM-DD'}"
        style="width:100%;"
      />`

    case 'time-picker':
      return `<el-time-picker
        v-model="timeValue"
        placeholder="${component.props?.placeholder || '选择时间'}"
        :disabled="${component.props?.disabled || false}"
        :clearable="${component.props?.clearable || true}"
        :readonly="${component.props?.readonly || false}"
        size="${component.props?.size || 'default'}"
        format="${component.props?.format || 'HH:mm:ss'}"
        value-format="${component.props?.valueFormat || 'HH:mm:ss'}"
        style="width:100%;"
      />`

    case 'rate':
      return `<el-rate
        v-model="rateValue"
        :max="${component.props?.max || 5}"
        :disabled="${component.props?.disabled || false}"
        :allow-half="${component.props?.allowHalf || false}"
        :low-threshold="${component.props?.lowThreshold || 2}"
        :high-threshold="${component.props?.highThreshold || 4}"
        :colors="${component.props?.colors || ['#F7BA2A', '#F7BA2A', '#F7BA2A']}"
        :void-color="${component.props?.voidColor || '#C6D1DE'}"
        :disabled-void-color="${component.props?.disabledVoidColor || '#EFF2F7'}"
        :show-text="${component.props?.showText || false}"
        :show-score="${component.props?.showScore || false}"
        :text-color="${component.props?.textColor || '#1F2D3D'}"
        size="${component.props?.size || 'default'}"
      />`

    case 'color-picker':
      return `<el-color-picker
        v-model="colorValue"
        :disabled="${component.props?.disabled || false}"
        :size="${component.props?.size || 'default'}"
        :show-alpha="${component.props?.showAlpha || false}"
        :color-format="${component.props?.colorFormat || 'hex'}"
        :predefine="${component.props?.predefine || []}"
      />`

    default:
      return `<div style="width:100%;height:100%;display:flex;align-items:center;justify-content:center;">
        <el-tag type="info">${component.type || '未知组件'}</el-tag>
      </div>`
  }
}

// 生成WindowTitleBar组件
function generateWindowTitleBarComponent(): string {
  return `<template>
  <div class="window-title-bar" :class="{ 'dark-mode': isDarkMode }">
    <div class="title-bar-left">
      <el-icon v-if="showLogo && !logoPath" :size="18"><Monitor /></el-icon>
      <img v-if="showLogo && logoPath" :src="logoPath" alt="应用图标" class="app-logo" />
      <span class="window-title">{{ title }}</span>
    </div>
    <div class="title-bar-controls">
      <button v-if="showDarkToggle" class="control-btn" @click="$emit('dark-toggle')">
        <el-icon :size="14"><component :is="isDarkMode ? 'Sunny' : 'Moon'" /></el-icon>
      </button>
      <button v-if="showMinimize" class="control-btn" @click="$emit('minimize')">
        <el-icon :size="14"><Minus /></el-icon>
      </button>
      <button v-if="showMaximize" class="control-btn" @click="$emit('maximize')">
        <el-icon :size="14"><FullScreen /></el-icon>
      </button>
      <button v-if="showClose" class="control-btn close-btn" @click="$emit('close')">
        <el-icon :size="14"><Close /></el-icon>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Close, FullScreen, Minus, Moon, Sunny, Monitor } from '@element-plus/icons-vue'

defineProps<{
  title: string
  showLogo: boolean
  logoPath: string
  showDarkToggle: boolean
  showMinimize: boolean
  showMaximize: boolean
  showClose: boolean
  isDarkMode: boolean
}>()

defineEmits<{
  'dark-toggle': []
  'minimize': []
  'maximize': []
  'close': []
}>()
</script>

<style scoped>
.window-title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 20px;
  height: 36px;
  background: transparent;
  user-select: none;
}

.window-title-bar.dark-mode {
  background: linear-gradient(90deg, #2c2c2c 0%, #3a3a3a 100%);
  color: #ffffff;
}

.title-bar-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.app-logo {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.window-title {
  font-weight: 600;
  font-size: 14px;
}

.title-bar-controls {
  display: flex;
  gap: 2px;
}

.control-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: var(--el-fill-color-light);
}

.close-btn:hover {
  background: var(--el-color-danger);
  color: #ffffff;
}
</style>
`
}
