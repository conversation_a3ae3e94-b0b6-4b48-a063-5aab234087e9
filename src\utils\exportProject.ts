import JSZip from 'jszip'
import { saveAs } from 'file-saver'
import type { WindowData, ComponentData } from '@/stores/designer'

// 导出项目为完整的Vue3+ElementPlus项目
export async function exportProject(windows: WindowData[], projectName: string = 'easy-window-project') {
  const zip = new JSZip()

  // 创建项目基础结构
  await createProjectStructure(zip, windows, projectName)

  // 生成ZIP文件并下载
  const content = await zip.generateAsync({ type: 'blob' })
  saveAs(content, `${projectName}.zip`)
}

// 创建项目结构
async function createProjectStructure(zip: JSZip, windows: WindowData[], projectName: string) {
  // 1. 创建package.json
  zip.file('package.json', generatePackageJson(projectName))

  // 2. 创建vite.config.ts
  zip.file('vite.config.ts', generateViteConfig())

  // 3. 创建tsconfig.json
  zip.file('tsconfig.json', generateTsConfig())

  // 4. 创建index.html
  zip.file('index.html', generateIndexHtml(projectName))

  // 5. 创建src目录结构
  const srcFolder = zip.folder('src')!
  
  // 创建main.ts
  srcFolder.file('main.ts', generateMainTs())

  // 创建App.vue
  srcFolder.file('App.vue', generateAppVue())

  // 创建路由配置
  const routerFolder = srcFolder.folder('router')!
  routerFolder.file('index.ts', generateRouterConfig(windows))

  // 创建窗口组件
  const viewsFolder = srcFolder.folder('views')!
  for (const window of windows) {
    viewsFolder.file(`${window.name}.vue`, generateWindowComponent(window))
  }

  // 创建组件文件夹
  const componentsFolder = srcFolder.folder('components')!
  componentsFolder.file('WindowComponent.vue', generateWindowComponentBase())

  // 创建样式文件
  srcFolder.file('style.css', generateGlobalStyles())

  // 6. 创建public目录
  const publicFolder = zip.folder('public')!
  
  // 复制LOGO文件（如果有的话）
  for (const window of windows) {
    if (window.titleBarConfig?.logoPath && window.titleBarConfig.logoPath.startsWith('/logos/')) {
      // 这里可以添加LOGO文件的复制逻辑
    }
  }

  // 7. 创建README.md
  zip.file('README.md', generateReadme(projectName))

  // 8. 创建.gitignore
  zip.file('.gitignore', generateGitignore())
}

// 生成package.json
function generatePackageJson(projectName: string): string {
  return JSON.stringify({
    name: projectName.toLowerCase().replace(/\s+/g, '-'),
    private: true,
    version: '0.0.0',
    type: 'module',
    scripts: {
      dev: 'vite',
      build: 'vue-tsc && vite build',
      preview: 'vite preview'
    },
    dependencies: {
      vue: '^3.4.0',
      'vue-router': '^4.2.5',
      'element-plus': '^2.4.4',
      '@element-plus/icons-vue': '^2.3.1'
    },
    devDependencies: {
      '@types/node': '^20.10.0',
      '@vitejs/plugin-vue': '^4.5.2',
      typescript: '^5.2.2',
      'vue-tsc': '^1.8.25',
      vite: '^5.0.8'
    }
  }, null, 2)
}

// 生成vite.config.ts
function generateViteConfig(): string {
  return `import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    open: true
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router'],
          elementPlus: ['element-plus', '@element-plus/icons-vue']
        }
      }
    }
  }
})
`
}

// 生成tsconfig.json
function generateTsConfig(): string {
  return JSON.stringify({
    compilerOptions: {
      target: 'ES2020',
      useDefineForClassFields: true,
      lib: ['ES2020', 'DOM', 'DOM.Iterable'],
      module: 'ESNext',
      skipLibCheck: true,
      moduleResolution: 'bundler',
      allowImportingTsExtensions: true,
      resolveJsonModule: true,
      isolatedModules: true,
      noEmit: true,
      jsx: 'preserve',
      strict: true,
      noUnusedLocals: true,
      noUnusedParameters: true,
      noFallthroughCasesInSwitch: true,
      baseUrl: '.',
      paths: {
        '@/*': ['src/*']
      }
    },
    include: ['src/**/*.ts', 'src/**/*.d.ts', 'src/**/*.tsx', 'src/**/*.vue'],
    references: [{ path: './tsconfig.node.json' }]
  }, null, 2)
}

// 生成index.html
function generateIndexHtml(projectName: string): string {
  return `<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>${projectName}</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
`
}

// 生成main.ts
function generateMainTs(): string {
  return `import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import router from './router'
import App from './App.vue'
import './style.css'

const app = createApp(App)

// 注册Element Plus
app.use(ElementPlus)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册路由
app.use(router)

app.mount('#app')
`
}

// 生成App.vue
function generateAppVue(): string {
  return `<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
// 应用根组件
</script>

<style>
#app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}
</style>
`
}

// 生成全局样式
function generateGlobalStyles(): string {
  return `/* 全局样式 */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

* {
  box-sizing: border-box;
}

/* 窗口容器样式 */
.window-container {
  position: relative;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  overflow: hidden;
}

/* 组件基础样式 */
.component-item {
  position: absolute;
  transition: all 0.2s ease;
}

/* 暗黑模式支持 */
.dark-mode {
  background: #1a1a1a;
  color: #ffffff;
}

.dark-mode .window-container {
  background: #2c2c2c;
  border-color: #404040;
}
`
}

// 生成README.md
function generateReadme(projectName: string): string {
  return `# ${projectName}

这是一个由 Easy Window 可视化设计器生成的 Vue3 + Element Plus 项目。

## 项目结构

\`\`\`
${projectName}/
├── src/
│   ├── views/          # 窗口组件
│   ├── components/     # 公共组件
│   ├── router/         # 路由配置
│   ├── App.vue         # 根组件
│   ├── main.ts         # 入口文件
│   └── style.css       # 全局样式
├── public/             # 静态资源
├── package.json        # 项目配置
├── vite.config.ts      # Vite 配置
├── tsconfig.json       # TypeScript 配置
└── README.md           # 项目说明
\`\`\`

## 开发

\`\`\`bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
\`\`\`

## 技术栈

- Vue 3
- TypeScript
- Element Plus
- Vue Router
- Vite

## 说明

此项目由 Easy Window 可视化设计器自动生成，包含完整的项目结构和配置文件。
您可以直接运行 \`npm install\` 和 \`npm run dev\` 来启动项目。
`
}

// 生成.gitignore
function generateGitignore(): string {
  return `# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
`
}

// 生成路由配置
function generateRouterConfig(windows: WindowData[]): string {
  const routes = windows.map(window => {
    return `  {
    path: '/${window.id}',
    name: '${window.name}',
    component: () => import('@/views/${window.name}.vue'),
    meta: {
      title: '${window.titleBarConfig?.title || window.name}'
    }
  }`
  }).join(',\n')

  return `import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/${windows[0]?.id || 'main'}'
  },
${routes}
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }
  next()
})

export default router
`
}

// 生成窗口组件
function generateWindowComponent(window: WindowData): string {
  const components = window.components.map(comp => generateComponentCode(comp)).join('\n    ')

  const titleBarHtml = window.windowType === 'normal' ? `
        <!-- 标题栏 -->
        <template #header>
          <div class="window-title-bar" :class="{ 'dark-mode': isDarkMode }">
            <div class="title-bar-left">
              ${window.titleBarConfig?.showLogo && window.titleBarConfig?.logoPath ?
                `<img src="${window.titleBarConfig.logoPath}" alt="应用图标" class="app-logo" />` :
                window.titleBarConfig?.showLogo ? '<el-icon :size="18"><Monitor /></el-icon>' : ''
              }
              <span class="window-title">${window.titleBarConfig?.title || window.name}</span>
            </div>
            <div class="title-bar-controls">
              ${window.titleBarConfig?.showDarkToggle ? `
              <button class="control-btn" @click="toggleDarkMode">
                <el-icon :size="14"><component :is="isDarkMode ? 'Sunny' : 'Moon'" /></el-icon>
              </button>` : ''}
              ${window.titleBarConfig?.showMinimize ? `
              <button class="control-btn" @click="minimize">
                <el-icon :size="14"><Minus /></el-icon>
              </button>` : ''}
              ${window.titleBarConfig?.showMaximize ? `
              <button class="control-btn" @click="maximize">
                <el-icon :size="14"><FullScreen /></el-icon>
              </button>` : ''}
              ${window.titleBarConfig?.showClose ? `
              <button class="control-btn close-btn" @click="closeWindow">
                <el-icon :size="14"><Close /></el-icon>
              </button>` : ''}
            </div>
          </div>
        </template>` : ''

  return `<template>
  <div class="window-page" :class="{ 'dark-mode': isDarkMode }">
    <div class="window-container-wrapper">
      <el-card
        class="window-container"
        :class="{ 'window-dark-mode': isDarkMode }"
        :style="windowStyle"
        :body-style="bodyStyle"
        :shadow="${window.cardConfig?.shadow || 'always'}"
      >${titleBarHtml}

        <!-- 窗口内容 -->
        <div class="window-content" :class="{ 'window-dark-mode': isDarkMode }">
          ${components}
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const isDarkMode = ref(${window.titleBarConfig?.isDarkMode || false})

// 计算属性
const windowStyle = computed(() => ({
  width: '${window.windowConfig.width}px',
  height: '${window.windowConfig.height}px',
  borderRadius: '${window.cardConfig?.borderRadius || 6}px',
  backgroundColor: '${window.cardConfig?.backgroundColor || '#ffffff'}',
  borderColor: '${window.cardConfig?.borderColor || '#ebeef5'}',
  borderWidth: '${window.cardConfig?.borderWidth || 1}px'
}))

const bodyStyle = computed(() => ({
  padding: '${window.cardConfig?.bodyPadding || 20}px',
  height: '100%',
  position: 'relative'
}))

// 方法
const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value
  ElMessage.success(\`已切换到\${isDarkMode.value ? '暗黑' : '亮色'}模式\`)
}

const minimize = () => {
  ElMessage.info('最小化功能')
}

const maximize = () => {
  ElMessage.info('最大化功能')
}

const closeWindow = () => {
  ElMessage.info('关闭窗口功能')
}
</script>

<style scoped>
.window-page {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.window-page.dark-mode {
  background: #1a1a1a;
}

.window-container-wrapper {
  position: relative;
}

.window-container {
  position: relative;
}

.window-container.window-dark-mode {
  background: #1f1f1f !important;
  border-color: #404040 !important;
}

.window-title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 20px;
  height: ${window.titleBarConfig?.height || 36}px;
  background: transparent;
  user-select: none;
}

.window-title-bar.dark-mode {
  background: linear-gradient(90deg, #2c2c2c 0%, #3a3a3a 100%);
  color: #ffffff;
}

.title-bar-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.app-logo {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.window-title {
  font-weight: 600;
  font-size: 14px;
}

.title-bar-controls {
  display: flex;
  gap: 2px;
}

.control-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: var(--el-fill-color-light);
}

.close-btn:hover {
  background: var(--el-color-danger);
  color: #ffffff;
}

.window-content {
  position: relative;
  width: 100%;
  height: 100%;
  background: #ffffff;
  transition: all 0.3s ease;
}

.window-content.window-dark-mode {
  background: #1f1f1f;
  color: #ffffff;
}

/* 组件样式 */
.component-item {
  position: absolute;
  transition: all 0.2s ease;
}

/* 暗黑模式下的组件样式 */
.window-content.window-dark-mode .component-item :deep(.el-input) {
  --el-input-bg-color: #2c2c2c;
  --el-input-border-color: #404040;
  --el-input-text-color: #ffffff;
}

.window-content.window-dark-mode .component-item :deep(.el-button) {
  --el-button-bg-color: #404040;
  --el-button-border-color: #606060;
  --el-button-text-color: #ffffff;
}
</style>
`
}

// 生成单个组件代码
function generateComponentCode(component: ComponentData): string {
  const style = `left: ${component.x}px; top: ${component.y}px; width: ${component.width}px; height: ${component.height}px;`

  switch (component.type) {
    case 'input':
      return `<el-input
        class="component-item"
        style="${style}"
        placeholder="${component.props?.placeholder || '请输入内容'}"
        ${component.props?.disabled ? 'disabled' : ''}
        ${component.props?.clearable ? 'clearable' : ''}
        size="${component.props?.size || 'default'}"
      />`

    case 'button':
      return `<el-button
        class="component-item"
        style="${style}"
        type="${component.props?.type || 'default'}"
        size="${component.props?.size || 'default'}"
        ${component.props?.disabled ? 'disabled' : ''}
        ${component.props?.round ? 'round' : ''}
      >
        ${component.props?.text || '按钮'}
      </el-button>`

    case 'text':
      return `<span
        class="component-item"
        style="${style} font-size: ${component.props?.fontSize || '14px'}; color: ${component.props?.color || 'inherit'};"
      >
        ${component.props?.text || '文本内容'}
      </span>`

    case 'select':
      const options = component.props?.options || []
      const optionElements = options.map((opt: any) =>
        `<el-option label="${opt.label}" value="${opt.value}" ${opt.disabled ? 'disabled' : ''} />`
      ).join('\n        ')

      return `<el-select
        class="component-item"
        style="${style}"
        placeholder="${component.props?.placeholder || '请选择'}"
        ${component.props?.disabled ? 'disabled' : ''}
        ${component.props?.clearable ? 'clearable' : ''}
        size="${component.props?.size || 'default'}"
      >
        ${optionElements}
      </el-select>`

    case 'textarea':
      return `<el-input
        class="component-item"
        style="${style}"
        type="textarea"
        placeholder="${component.props?.placeholder || '请输入内容'}"
        :rows="${component.props?.rows || 3}"
        ${component.props?.disabled ? 'disabled' : ''}
      />`

    case 'checkbox':
      return `<el-checkbox
        class="component-item"
        style="${style}"
        ${component.props?.disabled ? 'disabled' : ''}
        size="${component.props?.size || 'default'}"
      >
        ${component.props?.label || '复选框'}
      </el-checkbox>`

    case 'radio':
      return `<el-radio
        class="component-item"
        style="${style}"
        label="${component.props?.value || 'option1'}"
        ${component.props?.disabled ? 'disabled' : ''}
        size="${component.props?.size || 'default'}"
      >
        ${component.props?.label || '单选框'}
      </el-radio>`

    case 'switch':
      return `<el-switch
        class="component-item"
        style="${style}"
        ${component.props?.disabled ? 'disabled' : ''}
        size="${component.props?.size || 'default'}"
        active-text="${component.props?.activeText || ''}"
        inactive-text="${component.props?.inactiveText || ''}"
      />`

    case 'slider':
      return `<el-slider
        class="component-item"
        style="${style}"
        :min="${component.props?.min || 0}"
        :max="${component.props?.max || 100}"
        :step="${component.props?.step || 1}"
        ${component.props?.disabled ? 'disabled' : ''}
        ${component.props?.showInput ? 'show-input' : ''}
      />`

    default:
      return `<div class="component-item" style="${style}">
        <el-tag type="info">${component.type || '未知组件'}</el-tag>
      </div>`
  }
}

// 生成基础窗口组件
function generateWindowComponentBase(): string {
  return `<template>
  <div class="window-component">
    <slot />
  </div>
</template>

<script setup lang="ts">
// 基础窗口组件
</script>

<style scoped>
.window-component {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
`
}
