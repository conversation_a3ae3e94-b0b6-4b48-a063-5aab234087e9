<template>
  <div class="property-panel">
    
    <!-- 标签页容器 -->
    <el-tabs v-model="activeTab" class="panel-tabs">
      
      <!-- 属性设置标签页 -->
      <el-tab-pane label="属性" name="properties">
        <div class="tab-content">
          
          <!-- 未选中组件时的提示 -->
          <div v-if="!selectedComponent" class="no-selection">
            <el-empty 
              description="请选择一个组件来编辑其属性" 
              :image-size="80"
            />
          </div>
          
          <!-- 选中窗口时显示窗口属性编辑器 -->
          <div v-else-if="selectedComponent.type === 'window'" class="property-editor">
            <!-- 只显示窗口相关属性，不显示x/y/width/height等组件专属属性 -->
            <div class="property-section">
              <h4 class="section-title">窗口基本信息</h4>
              <el-form label-width="80px" size="small">
                <el-form-item label="窗口类型">
                  <el-select v-model="windowTypeProxy">
                    <el-option label="有标题栏窗口" value="normal" />
                    <el-option label="无标题栏窗口" value="borderless" />
                  </el-select>
                </el-form-item>
                <el-form-item label="窗口名称">
                  <el-input :value="selectedComponent.name" disabled placeholder="窗口名称不可修改" />
                </el-form-item>
                <el-form-item label="窗口路由">
                  <el-input v-model="windowRoute" placeholder="请输入窗口路由" />
                  <div class="form-tip">路由格式：page1, page2, home 等</div>
                </el-form-item>
                <el-form-item label="窗口标题" v-if="windowTypeProxy === 'normal'">
                  <el-input v-model="windowTitle" placeholder="请输入窗口标题" />
                </el-form-item>
              </el-form>
            </div>
            
            <!-- 窗口尺寸 -->
            <div class="property-section">
              <h4 class="section-title">窗口尺寸</h4>
              <el-form label-width="80px" size="small">
                <el-form-item label="宽度">
                  <el-input-number v-model="windowConfig.width" :min="200" :max="2000" :step="10" controls-position="right" />
                  <span class="unit-text">px</span>
                </el-form-item>
                <el-form-item label="高度">
                  <el-input-number v-model="windowConfig.height" :min="100" :max="1500" :step="10" controls-position="right" />
                  <span class="unit-text">px</span>
                </el-form-item>
              </el-form>
            </div>



            <!-- 标题栏配置 - 仅在有标题栏窗口时显示 -->
            <div v-if="windowTypeProxy === 'normal' && showWindowTitleBar" class="property-section">
              <h4 class="section-title">标题栏配置</h4>
              <el-form label-width="80px" size="small">
                <el-form-item label="显示LOGO">
                  <el-switch v-model="titleBarShowLogo" />
                </el-form-item>
                <el-form-item label="LOGO图片" v-if="titleBarShowLogo">
                  <div class="logo-upload-container">
                    <el-input
                      v-model="titleBarLogoPath"
                      placeholder="请输入图片URL或选择预设LOGO"
                      style="margin-bottom: 8px;"
                    />
                    <div class="preset-logos">
                      <div class="preset-logo-item"
                           v-for="logo in presetLogos"
                           :key="logo.name"
                           :class="{ active: titleBarLogoPath === logo.path }"
                           @click="titleBarLogoPath = logo.path">
                        <img :src="logo.path" :alt="logo.name" />
                        <span>{{ logo.name }}</span>
                      </div>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="暗黑模式切换">
                  <el-switch v-model="titleBarShowDarkToggle" />
                </el-form-item>
                <el-form-item label="当前暗黑模式" v-if="titleBarShowDarkToggle">
                  <el-switch v-model="titleBarIsDarkMode" />
                </el-form-item>
                <el-form-item label="最小化按钮">
                  <el-switch v-model="titleBarShowMinimize" />
                </el-form-item>
                <el-form-item label="最大化按钮">
                  <el-switch v-model="titleBarShowMaximize" />
                </el-form-item>
                <el-form-item label="关闭按钮">
                  <el-switch v-model="titleBarShowClose" />
                </el-form-item>
                <el-form-item label="标题颜色">
                  <el-color-picker v-model="titleBarTitleColor" show-alpha />
                </el-form-item>
                <el-form-item label="背景颜色">
                  <el-color-picker v-model="titleBarBackgroundColor" show-alpha />
                </el-form-item>
                <el-form-item label="标题栏高度">
                  <el-input-number v-model="titleBarHeight" :min="24" :max="60" :step="2" controls-position="right" />
                  <span class="unit-text">px</span>
                </el-form-item>
              </el-form>
            </div>
          </div>
          
          <!-- 选中普通组件时显示组件属性编辑器 -->
          <div v-else-if="isComponent(selectedComponent)" class="property-editor">
            <!-- 组件基本信息 -->
            <div class="property-section">
              <h4 class="section-title">基本信息</h4>
              <el-form label-width="60px" size="small">
                <el-form-item label="类型">
                  <el-input :value="selectedComponent.type" disabled />
                </el-form-item>
                <el-form-item label="名称">
                  <el-input :value="selectedComponent.name" @input="updateProperty('name', $event)" />
                </el-form-item>
              </el-form>
            </div>
            
            <!-- 位置和尺寸 -->
            <div class="property-section">
              <h4 class="section-title">位置与尺寸</h4>
              <el-form label-width="60px" size="small">
                <el-form-item label="X坐标">
                  <el-input-number :model-value="selectedComponent.x" @update:model-value="(val: number) => updateProperty('x', val)" :min="0" :step="1" controls-position="right" />
                </el-form-item>
                <el-form-item label="Y坐标">
                  <el-input-number :model-value="selectedComponent.y" @update:model-value="(val: number) => updateProperty('y', val)" :min="0" :step="1" controls-position="right" />
                </el-form-item>
                <el-form-item label="宽度">
                  <el-input-number :model-value="selectedComponent.width" @update:model-value="(val: number) => updateProperty('width', val)" :min="1" :step="1" controls-position="right" />
                </el-form-item>
                <el-form-item label="高度">
                  <el-input-number :model-value="selectedComponent.height" @update:model-value="(val: number) => updateProperty('height', val)" :min="1" :step="1" controls-position="right" />
                </el-form-item>
              </el-form>
            </div>
            
            <!-- 组件属性 -->
            <div class="property-section">
              <h4 class="section-title">组件属性</h4>
              <ComponentPropsEditor :component="selectedComponent" @update="updateProps" />
            </div>
            
            <!-- 样式属性 -->
            <div class="property-section">
              <h4 class="section-title">样式</h4>
              <ComponentStyleEditor :component="selectedComponent" @update="updateStyle" />
            </div>
            
          </div>
          
        </div>
      </el-tab-pane>
      
      <!-- 服务标签页 -->
      <el-tab-pane label="服务" name="service">
        <div class="tab-content">
          <!-- 顶部添加服务组件按钮 -->
          <el-button type="primary" @click="showServiceDialog = true" style="margin-bottom: 16px;">
            +添加服务组件
          </el-button>
          <!-- 服务组件列表 -->
          <div v-if="serviceComponents.length === 0" style="color: #999; text-align: center; margin-top: 40px;">
            暂无服务组件，请点击上方按钮添加
          </div>
          <el-card
            v-for="(svc, idx) in serviceComponents"
            :key="svc.type"
            style="margin-bottom: 12px;"
            shadow="hover"
          >
            <div style="display: flex; align-items: center;">
              <el-icon style="margin-right: 8px;"><Document /></el-icon>
              <div style="flex: 1;">
                <div style="font-weight: 500;">{{ svc.name }}</div>
                <div style="font-size: 12px; color: #888;">服务组件</div>
              </div>
              <!-- 删除按钮 -->
              <el-button
                type="danger"
                size="small"
                circle
                @click.stop="removeServiceComponent(svc.type)"
                title="删除服务组件"
              >
                <el-icon><Minus /></el-icon>
              </el-button>
            </div>
          </el-card>
          <!-- 添加服务组件对话框 -->
          <el-dialog v-model="showServiceDialog" title="添加服务组件" width="480px">
            <div style="margin-bottom: 12px; color: #409EFF; font-size: 14px;">
              服务组件不会在画布上显示，但会在导出时包含，提供API和事件接口供外部系统调用。
            </div>
            <el-card
              v-for="svc in availableServiceList"
              :key="svc.type"
              style="margin-bottom: 12px; cursor: pointer;"
              shadow="never"
            >
              <div style="display: flex; align-items: center;">
                <el-icon style="margin-right: 12px; font-size: 22px;"><component :is="svc.icon" /></el-icon>
                <div style="flex: 1;">
                  <div style="font-weight: 500;">{{ svc.name }}</div>
                  <div style="font-size: 12px; color: #888;">{{ svc.desc }}</div>
                </div>
                <el-button
                  type="primary"
                  size="small"
                  circle
                  @click.stop="addServiceComponent(svc)"
                  :disabled="serviceComponents.some(item => item.type === svc.type)"
                >
                  <el-icon><Plus /></el-icon>
                </el-button>
              </div>
            </el-card>
            <template #footer>
              <el-button @click="showServiceDialog = false">关闭</el-button>
            </template>
          </el-dialog>
        </div>
      </el-tab-pane>
      
    </el-tabs>
    
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, watch } from 'vue'
import { useDesignerStore } from '@/stores/designer'
import { storeToRefs } from 'pinia'
import { ElMessage } from 'element-plus'
import ComponentPropsEditor from './ComponentPropsEditor.vue'
import ComponentStyleEditor from './ComponentStyleEditor.vue'
import { Document, Plus, Message, Files, Loading, InfoFilled, Minus } from '@element-plus/icons-vue'
import { h } from 'vue'

// 获取设计器状态
const designerStore = useDesignerStore()
const { selectedComponentId, activeWindow, windowConfig, serviceComponents } = storeToRefs(designerStore)

// 当前激活的标签页
const activeTab = ref('properties')

// 窗口配置属性 - 使用当前窗口的配置
const windowType = ref('normal') // 窗口类型
// 根据窗口类型自动计算是否显示标题栏
const showWindowTitleBar = computed(() => {
  return windowTypeProxy.value === 'normal'
})

// 窗口基本配置
const windowTitle = ref('无标题窗口') // 窗口标题
const windowRoute = ref('') // 窗口路由



// 标题栏配置属性
const titleBarShowLogo = ref(true) // 是否显示LOGO
const titleBarLogoPath = ref('') // LOGO图片路径
const titleBarShowDarkToggle = ref(true) // 是否显示暗黑模式切换
const titleBarIsDarkMode = ref(false) // 当前是否为暗黑模式
const titleBarShowMinimize = ref(true) // 是否显示最小化按钮
const titleBarShowMaximize = ref(true) // 是否显示最大化按钮
const titleBarShowClose = ref(true) // 是否显示关闭按钮
const titleBarTitleColor = ref('') // 标题颜色
const titleBarBackgroundColor = ref('') // 背景颜色
const titleBarHeight = ref(36) // 标题栏高度

// 预设LOGO列表
const presetLogos = ref([
  { name: 'Windows', path: '/logos/windows.svg' },
  { name: 'Chrome', path: '/logos/chrome.svg' },
  { name: 'VS Code', path: '/logos/vscode.svg' },
  { name: 'Folder', path: '/logos/folder.svg' },
  { name: 'App', path: '/logos/app.svg' },
  { name: 'Terminal', path: '/logos/terminal.svg' }
])

// 监听当前窗口变化，更新窗口配置
watch(activeWindow, (newWindow) => {
  if (newWindow) {
    // 更新窗口基本配置 - 使用titleBarConfig.title而不是窗口名称
    windowTitle.value = newWindow.titleBarConfig?.title || newWindow.name
    windowRoute.value = newWindow.route || ''



    // 同步标题栏配置
    if (newWindow.titleBarConfig) {
      titleBarShowLogo.value = newWindow.titleBarConfig.showLogo
      titleBarLogoPath.value = newWindow.titleBarConfig.logoPath
      titleBarShowDarkToggle.value = newWindow.titleBarConfig.showDarkToggle
      titleBarIsDarkMode.value = newWindow.titleBarConfig.isDarkMode
      titleBarShowMinimize.value = newWindow.titleBarConfig.showMinimize
      titleBarShowMaximize.value = newWindow.titleBarConfig.showMaximize
      titleBarShowClose.value = newWindow.titleBarConfig.showClose
      titleBarTitleColor.value = newWindow.titleBarConfig.titleColor
      titleBarBackgroundColor.value = newWindow.titleBarConfig.backgroundColor
      titleBarHeight.value = newWindow.titleBarConfig.height
    }
  }
}, { immediate: true })

// 注入多选id
const multiSelectedIds = inject('multiSelectedIds', ref<string[]>([]))

// 多选时的选中组件列表
const multiSelectedComponents = computed(() => {
  if (!multiSelectedIds.value || multiSelectedIds.value.length < 2) return []
  return designerStore.components.filter(c => multiSelectedIds.value.includes(c.id))
})

// 多选时的公共属性（仅支持基本属性和props，样式可扩展）
function getCommonValue(key: keyof typeof designerStore.components[0]) {
  if (!multiSelectedComponents.value.length) return undefined
  const first = multiSelectedComponents.value[0][key]
  return multiSelectedComponents.value.every(c => c[key] === first) ? first : ''
}
function getCommonProps(key: string) {
  if (!multiSelectedComponents.value.length) return undefined
  const first = multiSelectedComponents.value[0].props?.[key]
  return multiSelectedComponents.value.every(c => c.props && c.props[key] === first) ? first : ''
}

// 处理窗口类型变化 - 现在showWindowTitleBar是computed属性，会自动响应windowTypeProxy变化

// 更新窗口配置
const updateWindowConfig = (config: Partial<typeof windowConfig.value>) => {
  if (activeWindow.value) {
    designerStore.updateWindowConfig(activeWindow.value.id, config)
  }
}

// 更新窗口标题
const updateWindowTitle = (title: string) => {
  if (activeWindow.value) {
    // 更新窗口名称
    const window = designerStore.windows.find(w => w.id === activeWindow.value.id)
    if (window) {
      window.name = title
    }
  }
}

// 复制窗口代码
const copyWindowCode = async () => {
  try {
    await navigator.clipboard.writeText(windowCodePreview.value)
    ElMessage.success('窗口代码已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 响应式获取选中对象：选中窗口时直接返回activeWindow，选中组件时用getSelectedComponent
const selectedComponent = computed(() => {
  // 多选组件时，返回单个组件或null
  if (multiSelectedIds.value.length === 1) {
    const comp = designerStore.getSelectedComponent()
    console.log('selectedComponent - multiselect single:', comp)
    return comp
  }
  if (multiSelectedIds.value.length > 1) {
    console.log('selectedComponent - multiselect multiple')
    return null
  }
  
  // 单选时，检查是否选中了窗口
  const selectedId = designerStore.selectedComponentId
  console.log('selectedComponent - selectedId:', selectedId)
  if (selectedId === '__window__') {
    // 返回窗口对象，包含type和windowType字段
    const windowObj = {
      ...designerStore.activeWindow,
      type: 'window'
    }
    console.log('selectedComponent - returning window object:', windowObj)
    return windowObj
  }
  
  // 否则返回选中的组件
  const comp = designerStore.getSelectedComponent()
  console.log('selectedComponent - returning component:', comp)
  return comp
})

// 窗口代码预览
const windowCodePreview = computed(() => {
  const config = {
    type: windowType.value,
    title: windowTitle.value,
    width: windowConfig.value.width,
    height: windowConfig.value.height,
    backgroundColor: windowConfig.value.backgroundColor,
    showTitleBar: showWindowTitleBar.value,
    borderStyle: windowBorderStyle.value,
    borderRadius: windowBorderRadius.value,
    shadowIntensity: windowShadowIntensity.value,
    resizable: windowResizable.value,
    minimizeButton: showMinimizeBtn.value,
    maximizeButton: showMaximizeBtn.value,
    closeButton: showCloseBtn.value,
    topMost: windowTopMost.value,
    opacity: windowOpacity.value
  }
  
  return `// 易语言窗口配置代码
窗口类型 = "${config.type === 'borderless' ? '无标题栏窗口' : config.type === 'normal' ? '普通窗口' : '工具窗口'}"
窗口标题 = "${config.title}"
窗口宽度 = ${config.width}
窗口高度 = ${config.height}
背景颜色 = "${config.backgroundColor}"
显示标题栏 = ${config.showTitleBar ? '真' : '假'}
边框样式 = "${config.borderStyle}"
圆角大小 = ${config.borderRadius}
阴影强度 = ${config.shadowIntensity}
可调整大小 = ${config.resizable ? '真' : '假'}
最小化按钮 = ${config.minimizeButton ? '真' : '假'}
最大化按钮 = ${config.maximizeButton ? '真' : '假'}
关闭按钮 = ${config.closeButton ? '真' : '假'}
总在最前 = ${config.topMost ? '真' : '假'}
透明度 = ${config.opacity}%`
})

// 多选时批量移动
const moveX = ref(10)
const moveY = ref(10)
const batchMove = (dx: number, dy: number) => {
  multiSelectedComponents.value.forEach(c => {
    const x = Math.max(0, c.x + dx)
    const y = Math.max(0, c.y + dy)
    designerStore.updateComponent(c.id, { x, y })
  })
}

// 批量更新属性
const batchUpdateProperty = (key: string, value: any) => {
  multiSelectedComponents.value.forEach(c => {
    designerStore.updateComponent(c.id, { [key]: value })
  })
}
const batchUpdateProps = (props: Record<string, any>) => {
  multiSelectedComponents.value.forEach(c => {
    designerStore.updateComponent(c.id, { props: { ...c.props, ...props } })
  })
}
const batchUpdateStyle = (style: Record<string, any>) => {
  multiSelectedComponents.value.forEach(c => {
    designerStore.updateComponent(c.id, { style: { ...c.style, ...style } })
  })
}

// 更新组件属性
const updateProperty = (key: string, value: any) => {
  if (multiSelectedIds.value.length > 1) {
    batchUpdateProperty(key, value)
  } else if (selectedComponentId.value) {
    designerStore.updateComponent(selectedComponentId.value, {
      [key]: value
    })
  }
}

// 更新组件props
const updateProps = (props: Record<string, any>) => {
  if (multiSelectedIds.value.length > 1) {
    batchUpdateProps(props)
  } else if (selectedComponentId.value) {
    designerStore.updateComponent(selectedComponentId.value, {
      props
    })
  }
}

// 更新组件样式
const updateStyle = (style: Record<string, any>) => {
  if (multiSelectedIds.value.length > 1) {
    batchUpdateStyle(style)
  } else if (selectedComponentId.value) {
    designerStore.updateComponent(selectedComponentId.value, {
      style
    })
  }
}

// 服务组件对话框显示状态
const showServiceDialog = ref(false)
// 可添加的服务组件列表
const availableServiceList = [
  {
    type: 'dialog',
    name: 'Dialog对话框',
    desc: '提供弹出对话框功能，支持加载页面内容',
    icon: Document
  },
  {
    type: 'file-drop',
    name: '文件拖拽服务',
    desc: '为指定组件添加文件拖拽功能，支持多种文件类型',
    icon: Files
  },
  {
    type: 'loading',
    name: 'Loading加载',
    desc: '提供全屏加载遮罩功能',
    icon: Loading
  },
  {
    type: 'message',
    name: '消息提示',
    desc: '提供消息提示、反馈提醒等交互功能',
    icon: Message
  },
  {
    type: 'info-box',
    name: '信息框服务',
    desc: '提供消息框、确认框、输入框等交互功能',
    icon: InfoFilled
  }
]
// 添加服务组件方法，调用store
function addServiceComponent(svc: any) {
  designerStore.addServiceComponent({ type: svc.type, name: svc.name, desc: svc.desc })
  showServiceDialog.value = false
}
// 删除服务组件方法，调用store
function removeServiceComponent(type: string) {
  designerStore.removeServiceComponent(type)
}

// 类型守卫：判断是否为普通组件
function isComponent(obj: any): obj is { id: string; type: string; name: string; x: number; y: number; width: number; height: number; props: Record<string, any>; style: Record<string, any>; } {
  return obj && typeof obj === 'object' && 'x' in obj && 'y' in obj && 'width' in obj && 'height' in obj && 'props' in obj && 'style' in obj;
}

// 类型守卫：判断是否为窗口对象
function isWindow(obj: any): obj is { id: string; type: 'window'; name: string; windowType: string } {
  return obj && typeof obj === 'object' && obj.type === 'window' && 'windowType' in obj && 'id' in obj;
}

// 用computed实现windowType的双向绑定，直接和store交互
const windowTypeProxy = computed({
  get() {
    // 检查当前选中的是否为窗口
    const selected = selectedComponent.value
    console.log('windowTypeProxy get - selected:', selected)
    if (selected && selected.type === 'window') {
      const windowType = designerStore.activeWindow.windowType
      console.log('windowTypeProxy get windowType:', windowType)
      return windowType
    }
    console.log('windowTypeProxy get default: normal')
    return 'normal'
  },
  set(val: 'normal' | 'borderless') {
    console.log('windowTypeProxy set:', val)
    const selected = selectedComponent.value
    if (selected && selected.type === 'window') {
      console.log('Calling updateWindowType with:', designerStore.activeWindow.id, val)
      designerStore.updateWindowType(designerStore.activeWindow.id, val)
    } else {
      console.log('Not updating windowType - selected is not window:', selected)
    }
  }
})



// 标题栏配置变化监听器 - 实时同步到store
watch([titleBarShowLogo, titleBarLogoPath, titleBarShowDarkToggle, titleBarIsDarkMode,
       titleBarShowMinimize, titleBarShowMaximize, titleBarShowClose,
       titleBarTitleColor, titleBarBackgroundColor, titleBarHeight], () => {
  if (activeWindow.value) {
    designerStore.updateTitleBarConfig(activeWindow.value.id, {
      showLogo: titleBarShowLogo.value,
      logoPath: titleBarLogoPath.value,
      showDarkToggle: titleBarShowDarkToggle.value,
      isDarkMode: titleBarIsDarkMode.value,
      showMinimize: titleBarShowMinimize.value,
      showMaximize: titleBarShowMaximize.value,
      showClose: titleBarShowClose.value,
      titleColor: titleBarTitleColor.value,
      backgroundColor: titleBarBackgroundColor.value,
      height: titleBarHeight.value
    })
  }
})

// 窗口标题变化监听器 - 只更新标题栏显示，不修改窗口名称
watch(windowTitle, (newTitle) => {
  if (activeWindow.value) {
    designerStore.updateTitleBarConfig(activeWindow.value.id, {
      title: newTitle
    })
  }
})

// 窗口路由变化监听器 - 更新窗口路由
watch(windowRoute, (newRoute) => {
  if (activeWindow.value) {
    designerStore.updateWindowInfo(activeWindow.value.id, {
      route: newRoute
    })
  }
})

// 窗口类型双向绑定
watch(selectedComponent, (val) => {
  if (isWindow(val)) {
    windowType.value = String(val.windowType || 'normal')
  }
}, { immediate: true })
</script>

<style scoped>
/* 属性面板容器 */
.property-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 标签页 */
.panel-tabs {
  height: 100%;
}

.panel-tabs :deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow-y: auto;
}

/* 标签页内容 */
.tab-content {
  padding: 16px;
}

/* 未选中状态 */
.no-selection {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

/* 属性编辑器 */
.property-editor {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 属性分组 */
.property-section {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 16px;
}

.section-title {
  margin: 0;
  padding: 12px 16px;
  background-color: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color-lighter);
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.property-section .el-form {
  padding: 16px;
}

/* 表单样式调整 */
.property-section .el-form-item {
  margin-bottom: 16px;
}

.property-section .el-form-item:last-child {
  margin-bottom: 0;
}

.property-section .el-input-number {
  width: 100%;
}

.property-section .el-color-picker {
  width: 100%;
}

/* 单位文本 */
.unit-text {
  margin-left: 8px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 代码预览 */
.code-preview {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.code-preview :deep(.el-textarea__inner) {
  background-color: var(--el-fill-color-lighter);
  border: 1px solid var(--el-border-color);
  color: var(--el-text-color-primary);
  font-family: inherit;
}

/* 滑块样式调整 */
.property-section .el-slider {
  margin-right: 12px;
}

/* 开关样式调整 */
.property-section .el-switch {
  --el-switch-on-color: var(--el-color-primary);
}

/* 批量移动控件 */
.batch-move-controls {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

/* LOGO上传容器样式 */
.logo-upload-container {
  width: 100%;
}

.preset-logos {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 8px;
  margin-top: 8px;
}

.preset-logo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border: 2px solid var(--el-border-color-light);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--el-bg-color);
}

.preset-logo-item:hover {
  border-color: var(--el-color-primary-light-3);
  background: var(--el-color-primary-light-9);
}

.preset-logo-item.active {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-8);
}

.preset-logo-item img {
  width: 24px;
  height: 24px;
  object-fit: contain;
  margin-bottom: 4px;
}

.preset-logo-item span {
  font-size: 10px;
  color: var(--el-text-color-secondary);
  text-align: center;
  line-height: 1.2;
}

/* 表单提示样式 */
.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  line-height: 1.4;
}
</style>