<template>
  <div class="icon-selector">
    <!-- 图标选择按钮 -->
    <el-button @click="showDialog = true" :icon="selectedIconComponent" size="small">
      {{ selectedIcon ? selectedIcon.replace('Icon', '') : '选择图标' }}
    </el-button>
    
    <!-- 清空按钮 -->
    <el-button v-if="selectedIcon" @click="clearIcon" size="small" type="danger" plain>
      清空
    </el-button>
    
    <!-- 图标选择对话框 -->
    <el-dialog
      v-model="showDialog"
      title="选择图标"
      width="800px"
      :close-on-click-modal="false"
    >
      <!-- 搜索框 -->
      <div class="search-container">
        <el-input
          v-model="searchText"
          placeholder="搜索图标..."
          clearable
          prefix-icon="Search"
          size="large"
        />
      </div>
      
      <!-- 图标网格 -->
      <div class="icon-grid" v-loading="loading">
        <div
          v-for="iconName in paginatedIcons"
          :key="iconName"
          class="icon-item"
          :class="{ active: selectedIcon === iconName }"
          @click="selectIcon(iconName)"
        >
          <component :is="getIconComponent(iconName)" :size="24" />
          <span class="icon-name">{{ iconName.replace('Icon', '') }}</span>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="filteredIcons.length"
          layout="prev, pager, next, total"
          small
        />
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmSelection">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import * as TablerIcons from '@tabler/icons-vue'

interface Props {
  modelValue?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const showDialog = ref(false)
const searchText = ref('')
const selectedIcon = ref(props.modelValue || '')
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(48)

// 所有可用的图标名称
const allIcons = ref<string[]>([])

// 获取图标组件
const getIconComponent = (iconName: string) => {
  return (TablerIcons as any)[iconName]
}

// 当前选中图标的组件
const selectedIconComponent = computed(() => {
  if (selectedIcon.value) {
    return getIconComponent(selectedIcon.value)
  }
  return null
})

// 过滤后的图标
const filteredIcons = computed(() => {
  if (!searchText.value) {
    return allIcons.value
  }
  return allIcons.value.filter(iconName =>
    iconName.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

// 当前页显示的图标
const paginatedIcons = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredIcons.value.slice(start, end)
})

// 初始化图标列表
onMounted(() => {
  loading.value = true
  // 获取所有 Tabler 图标名称
  allIcons.value = Object.keys(TablerIcons).filter(key => 
    key.startsWith('Icon') && typeof (TablerIcons as any)[key] === 'object'
  ).sort()
  loading.value = false
})

// 监听搜索文本变化，重置页码
watch(searchText, () => {
  currentPage.value = 1
})

// 监听 props 变化
watch(() => props.modelValue, (newValue) => {
  selectedIcon.value = newValue || ''
})

// 选择图标
const selectIcon = (iconName: string) => {
  selectedIcon.value = iconName
}

// 清空图标
const clearIcon = () => {
  selectedIcon.value = ''
  emit('update:modelValue', '')
}

// 确认选择
const confirmSelection = () => {
  emit('update:modelValue', selectedIcon.value)
  showDialog.value = false
}
</script>

<style scoped>
.icon-selector {
  display: flex;
  gap: 8px;
  align-items: center;
}

.search-container {
  margin-bottom: 16px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 8px;
  max-height: 400px;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.icon-item:hover {
  background-color: var(--el-fill-color-light);
  border-color: var(--el-border-color-hover);
}

.icon-item.active {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
}

.icon-name {
  font-size: 12px;
  margin-top: 4px;
  text-align: center;
  word-break: break-all;
  line-height: 1.2;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  gap: 8px;
}
</style>
