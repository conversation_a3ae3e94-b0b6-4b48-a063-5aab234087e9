<template>
  <div class="preview-container" :class="{ 'dark-mode': isDarkMode }">
    <!-- 预览工具栏 -->
    <div class="preview-toolbar">
      <div class="toolbar-left">
        <el-button @click="goBack" :icon="ArrowLeft" size="small">返回设计器</el-button>
        <el-divider direction="vertical" />
        <span class="preview-title">预览模式</span>
      </div>
      
      <div class="toolbar-center">
        <!-- 窗口切换器 -->
        <el-select v-model="currentWindowId" @change="switchWindow" size="small" style="width: 200px;">
          <el-option
            v-for="window in windows"
            :key="window.id"
            :label="window.name"
            :value="window.id"
          />
        </el-select>
      </div>
      
      <div class="toolbar-right">
        <el-button @click="toggleDarkMode" :icon="isDarkMode ? Sunny : Moon" size="small">
          {{ isDarkMode ? '亮色模式' : '暗黑模式' }}
        </el-button>
        <el-button @click="refreshPreview" :icon="Refresh" size="small">刷新</el-button>
      </div>
    </div>

    <!-- 预览内容区域 -->
    <div class="preview-content">
      <div class="preview-window-container">
        <!-- 调试信息 -->
        <div v-if="!currentWindow && windows.length > 0" class="debug-panel">
          <el-alert
            title="调试信息"
            :description="`找到 ${windows.length} 个窗口，但当前窗口ID '${currentWindowId}' 无效`"
            type="warning"
            show-icon
            :closable="false"
          />
        </div>

        <!-- 渲染当前窗口 -->
        <PreviewWindow
          v-if="currentWindow"
          :window-data="currentWindow"
          :key="currentWindow.id"
        />
        <div v-else class="no-window">
          <el-empty :description="windows.length === 0 ? '设计器中没有窗口数据' : '没有找到当前窗口数据'" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useDesignerStore } from '@/stores/designer'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Moon, Sunny, Refresh } from '@element-plus/icons-vue'
import PreviewWindow from '@/components/PreviewWindow.vue'

const router = useRouter()
const designerStore = useDesignerStore()

// 响应式数据
const isDarkMode = ref(false)
const currentWindowId = ref('')
const previewWindows = ref([])

// 计算属性
const windows = computed(() => {
  // 优先使用预览数据，如果没有则使用store数据
  return previewWindows.value.length > 0 ? previewWindows.value : designerStore.windows
})
const currentWindow = computed(() =>
  windows.value.find(w => w.id === currentWindowId.value)
)

// 方法
const goBack = () => {
  router.push('/')
}

const switchWindow = (windowId: string) => {
  currentWindowId.value = windowId
}

const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value
}

const refreshPreview = () => {
  // 重新加载预览数据
  try {
    const savedData = localStorage.getItem('designer-preview-data')
    if (savedData) {
      const previewData = JSON.parse(savedData)
      console.log('Refreshing preview data:', previewData)

      if (previewData.windows) {
        previewWindows.value = previewData.windows

        // 如果当前窗口不存在，切换到第一个窗口
        if (!windows.value.find(w => w.id === currentWindowId.value)) {
          currentWindowId.value = windows.value.length > 0 ? windows.value[0].id : ''
        }

        ElMessage.success('预览数据已刷新')
      }
    } else {
      ElMessage.warning('没有找到预览数据，请从设计器重新打开预览')
    }
  } catch (error) {
    console.error('Failed to refresh preview data:', error)
    ElMessage.error('刷新预览数据失败')
  }
}

// 生命周期
onMounted(() => {
  // 尝试从localStorage加载预览数据
  try {
    const savedData = localStorage.getItem('designer-preview-data')
    if (savedData) {
      const previewData = JSON.parse(savedData)

      // 检查数据是否过期（5分钟）
      const isExpired = Date.now() - previewData.timestamp > 5 * 60 * 1000
      if (!isExpired && previewData.windows) {
        previewWindows.value = previewData.windows
      }
    }
  } catch (error) {
    console.error('Failed to load preview data:', error)
  }

  // 默认显示第一个窗口
  if (windows.value.length > 0) {
    currentWindowId.value = windows.value[0].id
  }
})
</script>

<style scoped>
.preview-container {
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.preview-container.dark-mode {
  background: #1a1a1a;
  color: #ffffff;
}

.preview-toolbar {
  height: 50px;
  background: #ffffff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-container.dark-mode .preview-toolbar {
  background: #2c2c2c;
  border-bottom-color: #404040;
  color: #ffffff;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.preview-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.preview-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: auto;
}

.preview-window-container {
  max-width: 100%;
  max-height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-window {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
}
</style>
