<template>
  <div
    class="canvas-component"
    :class="{
      'selected': isSelected,
      'multi-selected': isMultiSelected && !isSelected,
      'window-dark-mode': windowDarkMode
    }"
    :style="componentStyle"
    @click.stop="handleClick($event)"
    @mousedown.stop="onMouseDown"
  >
    <!-- 拖拽和缩放内容... -->
    <!-- 统一所有类型都用 .component-inner 容器 -->
    <div class="component-inner" :class="{ 'image-container': componentData.type === 'image' }">
      <el-input
        v-if="componentData.type === 'input'"
        :type="componentData.props?.inputType || 'text'"
        :placeholder="componentData.props?.placeholder || '请输入内容'"
        :disabled="componentData.props?.disabled"
        :readonly="componentData.props?.readonly"
        :clearable="componentData.props?.clearable"
        :show-password="componentData.props?.showPassword"
        :show-word-limit="componentData.props?.showWordLimit"
        :maxlength="componentData.props?.maxlength"
        :minlength="componentData.props?.minlength"
        :size="componentData.props?.size || 'default'"
        :prefix-icon="getIconComponent(componentData.props?.prefixIcon)"
        :suffix-icon="getIconComponent(componentData.props?.suffixIcon)"
        :autocomplete="componentData.props?.autocomplete || 'off'"
        :name="componentData.props?.name"
        :form="componentData.props?.form"
        :label="componentData.props?.label"
        :tabindex="componentData.props?.tabindex"
        :validate-event="componentData.props?.validateEvent !== false"
        style="width: 100%; height: 100%;"
      />
      <!--请使用elementplus组件作为组件设计-->
      <el-button v-else-if="componentData.type === 'button'"
        style="width:100%;height:100%;"
        :type="componentData.props?.type || 'primary'"
        :size="componentData.props?.size || 'default'"
        :plain="!!componentData.props?.plain"
        :round="!!componentData.props?.round"
        :circle="!!componentData.props?.circle"
        :icon="getIconComponent(componentData.props?.icon)"
        :disabled="!!componentData.props?.disabled"
        :loading="!!componentData.props?.loading"
        :autofocus="!!componentData.props?.autofocus"
        :native-type="componentData.props?.nativeType || 'button'"
      >
        {{ componentData.props?.text || '按钮' }}
      </el-button>
      <el-text
        v-else-if="componentData.type === 'text'"
        :type="componentData.props?.type || 'primary'"
        :size="componentData.props?.size || 'default'"
        :tag="componentData.props?.tag || 'span'"
        :truncated="componentData.props?.truncated"
        :line-clamp="componentData.props?.lineClamp"
        style="width:100%;height:100%;display:flex;align-items:center;justify-content:center;"
      >
        {{ componentData.props?.content || '文本内容' }}
      </el-text>
      <el-image
        v-else-if="componentData.type === 'image'"
        :src="componentData.props?.src || 'https://via.placeholder.com/150'"
        :alt="componentData.props?.alt || '图片'"
        :fit="componentData.props?.fit || 'cover'"
        :loading="componentData.props?.loading || 'eager'"
        :lazy="componentData.props?.lazy"
        :scroll-container="componentData.props?.scrollContainer"
        :preview-src-list="componentData.props?.previewSrcList"
        :z-index="componentData.props?.zIndex"
        :initial-index="componentData.props?.initialIndex || 0"
        :close-on-press-escape="componentData.props?.closeOnPressEscape !== false"
        :preview-teleported="componentData.props?.previewTeleported !== false"
        style="width: 100%; height: 100%; display: block;"
      />
      <!-- 选择器 -->
      <el-select
        v-else-if="componentData.type === 'select'"
        :placeholder="componentData.props?.placeholder || '请选择'"
        :disabled="componentData.props?.disabled"
        :clearable="componentData.props?.clearable"
        :multiple="componentData.props?.multiple"
        :size="componentData.props?.size || 'default'"
        style="width: 100%"
      >
        <el-option
          v-for="option in componentData.props?.options || []"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :disabled="option.disabled"
        />
      </el-select>

      <!-- 多行文本框 -->
      <el-input
        v-else-if="componentData.type === 'textarea'"
        type="textarea"
        :placeholder="componentData.props?.placeholder || '请输入内容'"
        :disabled="componentData.props?.disabled"
        :readonly="componentData.props?.readonly"
        :rows="componentData.props?.rows || 3"
        :maxlength="componentData.props?.maxlength"
        :show-word-limit="componentData.props?.showWordLimit"
        :resize="componentData.props?.resize || 'vertical'"
        style="width: 100%; height: 100%"
      />

      <!-- 复选框 -->
      <el-checkbox
        v-else-if="componentData.type === 'checkbox'"
        :disabled="componentData.props?.disabled"
        :indeterminate="componentData.props?.indeterminate"
        :size="componentData.props?.size || 'default'"
      >
        {{ componentData.props?.label || '复选框' }}
      </el-checkbox>

      <!-- 单选框 -->
      <el-radio
        v-else-if="componentData.type === 'radio'"
        :label="componentData.props?.value || 'option1'"
        :disabled="componentData.props?.disabled"
        :size="componentData.props?.size || 'default'"
      >
        {{ componentData.props?.label || '单选框' }}
      </el-radio>

      <!-- 开关 -->
      <el-switch
        v-else-if="componentData.type === 'switch'"
        :disabled="componentData.props?.disabled"
        :size="componentData.props?.size || 'default'"
        :active-text="componentData.props?.activeText"
        :inactive-text="componentData.props?.inactiveText"
        :active-color="componentData.props?.activeColor"
        :inactive-color="componentData.props?.inactiveColor"
      />

      <!-- 滑块 -->
      <el-slider
        v-else-if="componentData.type === 'slider'"
        :min="componentData.props?.min || 0"
        :max="componentData.props?.max || 100"
        :step="componentData.props?.step || 1"
        :disabled="componentData.props?.disabled"
        :show-input="componentData.props?.showInput"
        :show-stops="componentData.props?.showStops"
        :range="componentData.props?.range"
        style="width: 100%"
      />

      <!-- 日期选择器 -->
      <el-date-picker
        v-else-if="componentData.type === 'date-picker'"
        :type="componentData.props?.type || 'date'"
        :placeholder="componentData.props?.placeholder || '选择日期'"
        :disabled="componentData.props?.disabled"
        :clearable="componentData.props?.clearable"
        :size="componentData.props?.size || 'default'"
        style="width: 100%"
      />

      <!-- 时间选择器 -->
      <el-time-picker
        v-else-if="componentData.type === 'time-picker'"
        :placeholder="componentData.props?.placeholder || '选择时间'"
        :disabled="componentData.props?.disabled"
        :clearable="componentData.props?.clearable"
        :size="componentData.props?.size || 'default'"
        style="width: 100%"
      />

      <!-- 评分 -->
      <el-rate
        v-else-if="componentData.type === 'rate'"
        :max="componentData.props?.max || 5"
        :disabled="componentData.props?.disabled"
        :allow-half="componentData.props?.allowHalf"
        :show-text="componentData.props?.showText"
        :show-score="componentData.props?.showScore"
      />

      <!-- 颜色选择器 -->
      <el-color-picker
        v-else-if="componentData.type === 'color-picker'"
        :disabled="componentData.props?.disabled"
        :size="componentData.props?.size || 'default'"
        :show-alpha="componentData.props?.showAlpha"
        :color-format="componentData.props?.colorFormat"
      />

      <WindowTitleBar
        v-else-if="componentData.type === 'window-titlebar'"
        v-bind="componentData.props"
      />
      <div v-else class="default-component" style="width:100%;height:100%;">
        {{ componentData.name }}
      </div>
    </div>
    <!-- 8方向缩放控制点 - 只在单选时显示 -->
    <template v-if="isSelected">
      <div 
        v-for="dir in resizeDirs" 
        :key="dir" 
        class="resize-handle" 
        :class="'resize-' + dir"
        @mousedown.stop="e => onResizeMouseDown(e, dir)"
      ></div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, inject } from 'vue'
import { useDesignerStore } from '@/stores/designer'
import type { ComponentData } from '@/stores/designer'
import WindowTitleBar from './WindowTitleBar.vue'
import {
  IconHome, IconUser, IconSettings, IconSearch, IconPlus, IconMinus, IconX, IconCheck,
  IconArrowLeft, IconArrowRight, IconArrowUp, IconArrowDown, IconEdit, IconTrash,
  IconEye, IconEyeOff, IconHeart, IconStar, IconBell, IconMail, IconPhone, IconLock,
  IconDownload, IconUpload, IconShare, IconCopy, IconRefresh, IconZoomIn, IconZoomOut,
  IconPlayerStop, IconPlayerSkipBack, IconPlayerSkipForward,
  IconVolume, IconVolumeOff, IconWifi, IconBluetooth, IconBattery, IconCpu,
  IconCamera, IconVideo, IconPhoto, IconFile, IconFolder, IconCode, IconTerminal,
  IconBrandGithub, IconBrandGoogle, IconBrandFacebook, IconBrandTwitter,
  IconCalendar, IconClock, IconMap, IconMapPin, IconSun, IconMoon, IconCloud,
  IconBookmark, IconTag, IconFilter, IconSortAscending, IconList, IconGrid3x3,
  IconChartBar, IconChartPie, IconChartLine, IconDatabase, IconServer, IconShield,
  IconKey, IconQrcode, IconPalette, IconBrush, IconPencil, IconRocket, IconCar
} from '@tabler/icons-vue'

interface Props {
  componentData: ComponentData
  isSelected: boolean
}

interface Emits {
  (e: 'select', componentId: string, event?: MouseEvent): void
  (e: 'update', componentId: string, updates: any): void
  (e: 'align-lines', componentId: string, lines: { type: 'vertical' | 'horizontal', position: number }[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 注入window-container宽高用于边界限制，默认值也用ref
const windowWidth = inject('windowWidth', ref(800))
const windowHeight = inject('windowHeight', ref(600))
// 注入多选id列表
const multiSelectedIds = inject('multiSelectedIds', ref<string[]>([]))
// 注入窗口暗黑模式状态
const windowDarkMode = inject('windowDarkMode', ref(false))

const isMultiSelected = computed(() => multiSelectedIds.value.includes(props.componentData.id))

// 预定义的图标映射
const iconMap = {
  IconHome, IconUser, IconSettings, IconSearch, IconPlus, IconMinus, IconX, IconCheck,
  IconArrowLeft, IconArrowRight, IconArrowUp, IconArrowDown, IconEdit, IconTrash,
  IconEye, IconEyeOff, IconHeart, IconStar, IconBell, IconMail, IconPhone, IconLock,
  IconDownload, IconUpload, IconShare, IconCopy, IconRefresh, IconZoomIn, IconZoomOut,
  IconPlayerStop, IconPlayerSkipBack, IconPlayerSkipForward,
  IconVolume, IconVolumeOff, IconWifi, IconBluetooth, IconBattery, IconCpu,
  IconCamera, IconVideo, IconPhoto, IconFile, IconFolder, IconCode, IconTerminal,
  IconBrandGithub, IconBrandGoogle, IconBrandFacebook, IconBrandTwitter,
  IconCalendar, IconClock, IconMap, IconMapPin, IconSun, IconMoon, IconCloud,
  IconBookmark, IconTag, IconFilter, IconSortAscending, IconList, IconGrid3x3,
  IconChartBar, IconChartPie, IconChartLine, IconDatabase, IconServer, IconShield,
  IconKey, IconQrcode, IconPalette, IconBrush, IconPencil, IconRocket, IconCar
}

// 获取图标组件
const getIconComponent = (iconName: string) => {
  if (!iconName) return null
  return (iconMap as any)[iconName] || null
}

const componentStyle = computed(() => {
  // 先合并 style，再强制覆盖 width/height，防止 style 里的 width/height 覆盖受控属性
  const style = { ...props.componentData.style };
  style.width = props.componentData.width + 'px';
  style.height = props.componentData.height + 'px';
  return {
    position: 'absolute' as const,
    left: props.componentData.x + 'px',
    top: props.componentData.y + 'px',
    border: props.isSelected ? '2px solid #409eff' : isMultiSelected.value ? '2px dashed #95d5ff' : '1px solid transparent',
    boxSizing: 'border-box' as const,
    cursor: 'pointer',
    background: isMultiSelected.value && !props.isSelected ? 'rgba(64,158,255,0.08)' : undefined,
    ...style
  }
})

const handleClick = (event?: MouseEvent) => {
  emit('select', props.componentData.id, event)
}

const dragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const initialPos = ref({ x: 0, y: 0 })

// 多选拖动相关
const multiDragStart = ref<{ id: string; x: number; y: number }[]>([])

const onMouseDown = (e: MouseEvent) => {
  if (e.button !== 0) return // 只响应左键
  // 如果点在缩放点上则不触发拖动
  if ((e.target as HTMLElement).classList.contains('resize-handle')) return
  dragging.value = true
  dragStart.value = { x: e.clientX, y: e.clientY }
  initialPos.value = { x: props.componentData.x, y: props.componentData.y }
  // 多选拖动：记录所有多选组件初始位置
  if (multiSelectedIds.value.length > 1 && multiSelectedIds.value.includes(props.componentData.id)) {
    multiDragStart.value = multiSelectedIds.value.map(id => {
      const comp = store.components.find(c => c.id === id)
      return comp ? { id, x: comp.x, y: comp.y } : { id, x: 0, y: 0 }
    })
  } else {
    multiDragStart.value = []
  }
  window.addEventListener('mousemove', onMouseMove)
  window.addEventListener('mouseup', onMouseUp)
}

const alignLines = ref<{ type: 'vertical' | 'horizontal', position: number }[]>([])
const alignThreshold = 3 // px
const store = useDesignerStore()

// 新增：辅助线回调
const emitAlignLines = (lines: { type: 'vertical' | 'horizontal', position: number }[]) => {
  emit('align-lines', props.componentData.id, lines)
}

// 获取所有其它组件的边缘/中心
function getOtherComponentGuides() {
  return store.components
    .filter(c => c.id !== props.componentData.id)
    .map(c => ({
      left: c.x,
      right: c.x + c.width,
      top: c.y,
      bottom: c.y + c.height,
      vCenter: c.x + c.width / 2,
      hCenter: c.y + c.height / 2
    }))
}

// 返回吸附后的坐标和辅助线
function getSnappedPosition(x: number, y: number, width: number, height: number) {
  const guides = getOtherComponentGuides()
  const lines: { type: 'vertical' | 'horizontal', position: number }[] = []
  const self = {
    left: x,
    right: x + width,
    top: y,
    bottom: y + height,
    vCenter: x + width / 2,
    hCenter: y + height / 2
  }
  let snappedX = x
  let snappedY = y
  // 吸附优先级：左/右/中
  guides.forEach(g => {
    // 垂直吸附
    if (Math.abs(self.left - g.left) <= alignThreshold) {
      snappedX = g.left
      lines.push({ type: 'vertical', position: g.left })
    }
    if (Math.abs(self.left - g.right) <= alignThreshold) {
      snappedX = g.right
      lines.push({ type: 'vertical', position: g.right })
    }
    if (Math.abs(self.right - g.left) <= alignThreshold) {
      snappedX = g.left - width
      lines.push({ type: 'vertical', position: g.left })
    }
    if (Math.abs(self.right - g.right) <= alignThreshold) {
      snappedX = g.right - width
      lines.push({ type: 'vertical', position: g.right })
    }
    if (Math.abs(self.vCenter - g.vCenter) <= alignThreshold) {
      snappedX = g.vCenter - width / 2
      lines.push({ type: 'vertical', position: g.vCenter })
    }
    // 水平吸附
    if (Math.abs(self.top - g.top) <= alignThreshold) {
      snappedY = g.top
      lines.push({ type: 'horizontal', position: g.top })
    }
    if (Math.abs(self.top - g.bottom) <= alignThreshold) {
      snappedY = g.bottom
      lines.push({ type: 'horizontal', position: g.bottom })
    }
    if (Math.abs(self.bottom - g.top) <= alignThreshold) {
      snappedY = g.top - height
      lines.push({ type: 'horizontal', position: g.top })
    }
    if (Math.abs(self.bottom - g.bottom) <= alignThreshold) {
      snappedY = g.bottom - height
      lines.push({ type: 'horizontal', position: g.bottom })
    }
    if (Math.abs(self.hCenter - g.hCenter) <= alignThreshold) {
      snappedY = g.hCenter - height / 2
      lines.push({ type: 'horizontal', position: g.hCenter })
    }
  })
  // 新增：抛出辅助线数据
  emitAlignLines(lines)
  return { snappedX, snappedY, lines }
}

// 拖拽时检测辅助线并吸附
const onMouseMove = (e: MouseEvent) => {
  if (!dragging.value) return
  const dx = e.clientX - dragStart.value.x
  const dy = e.clientY - dragStart.value.y
  // 多选整体拖动
  if (multiDragStart.value.length > 1 && multiSelectedIds.value.includes(props.componentData.id)) {
    multiDragStart.value.forEach(item => {
      const comp = store.components.find(c => c.id === item.id)
      if (!comp) return
      let newX = Math.max(0, Math.min(item.x + dx, windowWidth.value - comp.width))
      let newY = Math.max(0, Math.min(item.y + dy, windowHeight.value - comp.height))
      emit('update', item.id, { x: newX, y: newY })
    })
    return
  }
  // 单选拖动
  let newX = initialPos.value.x + dx
  let newY = initialPos.value.y + dy
  newX = Math.max(0, Math.min(newX, windowWidth.value - props.componentData.width))
  newY = Math.max(0, Math.min(newY, windowHeight.value - props.componentData.height))
  // 吸附
  const { snappedX, snappedY, lines } = getSnappedPosition(newX, newY, props.componentData.width, props.componentData.height)
  alignLines.value = lines
  emit('update', props.componentData.id, {
    x: snappedX,
    y: snappedY
  })
}

// 8方向缩放
const resizing = ref(false)
const resizeDir = ref('se')
const resizeStart = ref({ x: 0, y: 0, width: 0, height: 0, left: 0, top: 0 })
// 针对window-titlebar类型设置更大最小宽高
const getMinWidth = () => props.componentData.type === 'window-titlebar' ? 120 : 20
const getMinHeight = () => props.componentData.type === 'window-titlebar' ? 36 : 20
const resizeDirs = ['nw','n','ne','e','se','s','sw','w']

const onResizeMouseDown = (e: MouseEvent, dir: string) => {
  e.preventDefault()
  resizing.value = true
  resizeDir.value = dir
  resizeStart.value = {
    x: e.clientX,
    y: e.clientY,
    width: props.componentData.width,
    height: props.componentData.height,
    left: props.componentData.x,
    top: props.componentData.y
  }
  window.addEventListener('mousemove', onResizeMouseMove)
  window.addEventListener('mouseup', onResizeMouseUp)
}

const onResizeMouseMove = (e: MouseEvent) => {
  if (!resizing.value) return
  const dx = e.clientX - resizeStart.value.x
  const dy = e.clientY - resizeStart.value.y
  let { width, height, left, top } = resizeStart.value
  let newWidth = width
  let newHeight = height
  let newLeft = left
  let newTop = top
  const wW = windowWidth.value
  const wH = windowHeight.value
  // 动态最小宽高
  const minWidth = getMinWidth()
  const minHeight = getMinHeight()
  switch (resizeDir.value) {
    case 'e':
      newWidth = Math.max(minWidth, Math.min(width + dx, wW - left))
      break
    case 's':
      newHeight = Math.max(minHeight, Math.min(height + dy, wH - top))
      break
    case 'se':
      newWidth = Math.max(minWidth, Math.min(width + dx, wW - left))
      newHeight = Math.max(minHeight, Math.min(height + dy, wH - top))
      break
    case 'w': {
      newWidth = Math.max(minWidth, width - dx)
      newLeft = left + dx
      if (newLeft < 0) {
        newLeft = 0
        newWidth = width + left
      }
      break
    }
    case 'n': {
      newHeight = Math.max(minHeight, height - dy)
      newTop = top + dy
      if (newTop < 0) {
        newTop = 0
        newHeight = height + top
      }
      break
    }
    case 'nw': {
      // 左上
      let dx2 = dx, dy2 = dy
      newWidth = Math.max(minWidth, width - dx2)
      newLeft = left + dx2
      if (newLeft < 0) {
        newLeft = 0
        newWidth = width + left
      }
      newHeight = Math.max(minHeight, height - dy2)
      newTop = top + dy2
      if (newTop < 0) {
        newTop = 0
        newHeight = height + top
      }
      break
    }
    case 'ne': {
      // 右上
      let dy2 = dy
      newWidth = Math.max(minWidth, Math.min(width + dx, wW - left))
      newHeight = Math.max(minHeight, height - dy2)
      newTop = top + dy2
      if (newTop < 0) {
        newTop = 0
        newHeight = height + top
      }
      break
    }
    case 'sw': {
      // 左下
      let dx2 = dx
      newWidth = Math.max(minWidth, width - dx2)
      newLeft = left + dx2
      if (newLeft < 0) {
        newLeft = 0
        newWidth = width + left
      }
      newHeight = Math.max(minHeight, Math.min(height + dy, wH - newTop))
      break
    }
  }
  // 限制不超出右下边界
  if (newLeft + newWidth > wW) {
    newWidth = wW - newLeft
  }
  if (newTop + newHeight > wH) {
    newHeight = wH - newTop
  }
  // 缩放吸附
  const { snappedX, snappedY, lines } = getSnappedPosition(newLeft, newTop, newWidth, newHeight)
  alignLines.value = lines
  emit('update', props.componentData.id, {
    x: snappedX,
    y: snappedY,
    width: newWidth,
    height: newHeight
  })
}

// 拖拽/缩放结束时清除辅助线
const clearAlignLines = () => { alignLines.value = [] }
const onMouseUp = () => {
  dragging.value = false
  clearAlignLines()
  window.removeEventListener('mousemove', onMouseMove)
  window.removeEventListener('mouseup', onMouseUp)
}
const onResizeMouseUp = () => {
  resizing.value = false
  clearAlignLines()
  window.removeEventListener('mousemove', onResizeMouseMove)
  window.removeEventListener('mouseup', onResizeMouseUp)
}
</script>

<style scoped>
.canvas-component {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: box-shadow 0.15s, border 0.15s;
}
.canvas-component.selected {
  box-shadow: 0 0 0 1px #409eff;
  z-index: 10;
}
.canvas-component.multi-selected {
  box-shadow: 0 0 0 1px #95d5ff;
  z-index: 9;
}

.component-inner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 统一 input 拉伸样式到 .component-inner 下，移除 .input-inner */
.component-inner :deep(.el-input),
.component-inner :deep(.el-input__wrapper),
.component-inner :deep(.el-input__inner) {
  width: 100% !important;
  height: 100% !important;
  min-width: 0 !important;
  min-height: 0 !important;
  box-sizing: border-box !important;
  padding: 0 !important;
}

/* 让el-input内容随父容器拉伸，仅作用于 input-inner，避免影响其它组件 */
.input-inner :deep(.el-input),
.input-inner :deep(.el-input__wrapper),
.input-inner :deep(.el-input__inner) {
  width: 100% !important;
  height: 100% !important;
  min-width: 0 !important;
  min-height: 0 !important;
  box-sizing: border-box !important;
  padding: 0 !important;
}

.default-component {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  color: #909399;
  font-size: 12px;
}

/* 8点缩放控制点样式 */
.resize-handle {
  position: absolute;
  width: 10px;
  height: 10px;
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border-radius: 50%;
  z-index: 10;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(64,158,255,0.3), 0 1px 3px rgba(0,0,0,0.12);
  transition: all 0.2s ease;
}
/* 缩放控制点悬停效果 */
.resize-handle:hover {
  transform: scale(1.2);
  box-shadow: 0 3px 12px rgba(64,158,255,0.4), 0 2px 6px rgba(0,0,0,0.15);
}
.resize-nw { left: -5px; top: -5px; cursor: nwse-resize; }
.resize-n { left: 50%; top: -5px; cursor: ns-resize;  }
.resize-ne { right: -5px; top: -5px; cursor: nesw-resize; }
.resize-e { right: -5px; top: 50%; cursor: ew-resize;  }
.resize-se { right: -5px; bottom: -5px; cursor: nwse-resize; }
.resize-s { left: 50%; bottom: -5px; cursor: ns-resize;  }
.resize-sw { left: -5px; bottom: -5px; cursor: nesw-resize; }
.resize-w { left: -5px; top: 50%; cursor: ew-resize;  }

/* 图片组件样式 */
.component-inner :deep(.el-image) {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  flex: none !important;
  position: relative !important;
}

.component-inner :deep(.el-image__inner) {
  width: 100% !important;
  height: 100% !important;
  object-fit: inherit !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
}

/* 图片组件容器特殊处理 - 避免flex布局影响 */
.component-inner.image-container {
  display: block !important;
  position: relative !important;
}

/* 让WindowTitleBar内容自适应拉伸 */
.component-inner :deep(.window-titlebar-root) {
  width: 100% !important;
  height: 100% !important;
  min-width: 0 !important;
  min-height: 0 !important;
  box-sizing: border-box !important;
  padding: 0 !important;
}
</style>