{"name": "easy-window", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tabler/icons-vue": "^2.47.0", "element-plus": "^2.4.4", "file-saver": "^2.0.5", "interactjs": "^1.10.19", "jszip": "^3.10.1", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.9.0", "@vitejs/plugin-vue": "^4.5.0", "typescript": "^5.2.2", "vite": "^5.0.0", "vue-tsc": "^1.8.22"}}