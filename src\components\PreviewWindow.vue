<template>
  <div class="preview-window">
    <!-- 模拟窗口 - 使用与设计器相同的结构 -->
    <div
      class="window-container preview-mode"
      :class="{ 'window-dark-mode': windowData.titleBarConfig?.isDarkMode }"
      :style="windowStyle"
    >
      <!-- 标题栏 - 仅在normal类型窗口显示 -->
      <div v-if="windowData.windowType === 'normal'" class="preview-window-header">
        <div
          class="preview-title-bar"
          :class="{ 'dark-mode': windowData.titleBarConfig?.isDarkMode }"
          :style="{
            background: windowData.titleBarConfig?.backgroundColor || undefined,
            color: windowData.titleBarConfig?.titleColor || undefined,
            height: (windowData.titleBarConfig?.height || 36) + 'px',
            minHeight: (windowData.titleBarConfig?.height || 36) + 'px'
          }"
        >
          <!-- 左侧：应用LOGO和标题 -->
          <div class="title-bar-left">
            <!-- 应用LOGO -->
            <div
              v-if="windowData.titleBarConfig?.showLogo && windowData.titleBarConfig?.logoPath"
              class="app-logo-container"
            >
              <img
                :src="windowData.titleBarConfig.logoPath"
                alt="应用图标"
                class="app-logo"
                @error="handleLogoError"
              />
            </div>
            <!-- 默认图标 -->
            <div v-else-if="windowData.titleBarConfig?.showLogo" class="default-icon">
              <el-icon :size="18"><Monitor /></el-icon>
            </div>
            <!-- 窗口标题 -->
            <span class="window-title">
              {{ windowData.titleBarConfig?.title || windowData.name || '无标题窗口' }}
            </span>
          </div>

          <!-- 右侧：控制按钮 -->
          <div class="title-bar-controls">
            <!-- 暗黑模式切换按钮 -->
            <el-tooltip v-if="windowData.titleBarConfig?.showDarkToggle" content="切换暗黑模式" placement="bottom">
              <button
                class="title-control-btn dark-toggle-btn"
                :class="{ active: windowData.titleBarConfig?.isDarkMode }"
                @click="handleDarkToggle"
              >
                <el-icon :size="14">
                  <component :is="windowData.titleBarConfig?.isDarkMode ? Sunny : Moon" />
                </el-icon>
              </button>
            </el-tooltip>

            <!-- 最小化按钮 -->
            <el-tooltip v-if="windowData.titleBarConfig?.showMinimize" content="最小化" placement="bottom">
              <button class="title-control-btn minimize-btn" @click="handleMinimize">
                <el-icon :size="14"><Minus /></el-icon>
              </button>
            </el-tooltip>

            <!-- 最大化按钮 -->
            <el-tooltip v-if="windowData.titleBarConfig?.showMaximize" content="最大化" placement="bottom">
              <button class="title-control-btn maximize-btn" @click="handleMaximize">
                <el-icon :size="14"><FullScreen /></el-icon>
              </button>
            </el-tooltip>

            <!-- 关闭按钮 -->
            <el-tooltip v-if="windowData.titleBarConfig?.showClose" content="关闭" placement="bottom">
              <button class="title-control-btn close-btn" @click="handleClose">
                <el-icon :size="14"><Close /></el-icon>
              </button>
            </el-tooltip>
          </div>
        </div>
      </div>

      <!-- 窗口内容区域 -->
      <div
        class="preview-content-area"
        :class="{ 'window-dark-mode': windowData.titleBarConfig?.isDarkMode }"
        :style="cardBodyStyle"
      >
        <!-- 调试信息 -->
        <div v-if="windowData.components.length === 0" class="debug-info">
          <el-alert
            title="调试信息"
            :description="`当前窗口 '${windowData.name}' 没有组件数据`"
            type="info"
            show-icon
            :closable="false"
          />
        </div>

        <!-- 渲染所有组件 -->
        <PreviewComponent
          v-for="component in windowData.components"
          :key="component.id"
          :component-data="component"
          :window-dark-mode="windowData.titleBarConfig?.isDarkMode || false"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, provide } from 'vue'
import { ElMessage } from 'element-plus'
import { Close, FullScreen, Minus, Moon, Sunny, Monitor } from '@element-plus/icons-vue'
import type { WindowData } from '@/stores/designer'
import PreviewComponent from './PreviewComponent.vue'

// 定义props
const props = defineProps<{
  windowData: WindowData
}>()

// 定义emits
const emit = defineEmits<{
  darkToggle: []
  minimize: []
  maximize: []
  close: []
}>()

// 计算属性
const windowStyle = computed(() => {
  return {
    width: props.windowData.windowConfig.width + 'px',
    height: props.windowData.windowConfig.height + 'px',
    borderRadius: '6px',
    backgroundColor: '#ffffff',
    border: '1px solid #ebeef5'
  }
})

const cardBodyStyle = computed(() => {
  return {
    padding: '20px',
    height: '100%',
    position: 'relative'
  }
})

// provide 窗口暗黑模式状态给子组件
provide('windowDarkMode', computed(() => props.windowData.titleBarConfig?.isDarkMode ?? false))

// 事件处理函数
const handleDarkToggle = () => {
  ElMessage.info('预览模式下暗黑模式切换演示')
  emit('darkToggle')
}

const handleMinimize = () => {
  ElMessage.info('最小化功能演示')
  emit('minimize')
}

const handleMaximize = () => {
  ElMessage.info('最大化功能演示')
  emit('maximize')
}

const handleClose = () => {
  ElMessage.info('关闭功能演示')
  emit('close')
}

const handleLogoError = (event: Event) => {
  console.warn('LOGO加载失败:', props.windowData.titleBarConfig?.logoPath)
  // 可以在这里设置默认图标或者隐藏LOGO
}
</script>

<style scoped>
.preview-window {
  display: flex;
  align-items: center;
  justify-content: center;
}

.window-container.preview-mode {
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
}

/* 预览模式下的标题栏样式 */
.preview-title-bar {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
  padding: 8px 20px;
  height: 36px;
  min-height: 36px;
  background: transparent;
  user-select: none;
  transition: all 0.3s ease;
  border-bottom: none;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
}

.preview-title-bar.dark-mode {
  background: linear-gradient(90deg, #2c2c2c 0%, #3a3a3a 100%);
  color: #ffffff;
}

/* 复用设计器中的标题栏样式 */
.title-bar-left {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
}

.app-logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 3px;
  overflow: hidden;
  background: var(--el-fill-color-lighter);
  border: 1px solid var(--el-border-color-light);
}

.app-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.default-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 3px;
  background: var(--el-color-primary);
  color: white;
}

.window-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-title-bar.dark-mode .window-title {
  color: #ffffff;
}

.title-bar-controls {
  display: flex;
  align-items: center;
  gap: 2px;
}

.title-control-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: var(--el-text-color-secondary);
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-control-btn:hover {
  background: var(--el-fill-color-light);
  transform: scale(1.05);
}

.dark-toggle-btn {
  color: var(--el-color-warning) !important;
}

.dark-toggle-btn.active {
  background: var(--el-color-warning-light-9) !important;
}

.minimize-btn:hover {
  background: var(--el-color-info-light-8) !important;
  color: var(--el-color-info) !important;
}

.maximize-btn:hover {
  background: var(--el-color-success-light-8) !important;
  color: var(--el-color-success) !important;
}

.close-btn:hover {
  background: var(--el-color-danger) !important;
  color: #ffffff !important;
}

/* 预览内容区域 */
.preview-content-area {
  position: relative;
  width: 100%;
  height: 100%;
  background: #ffffff;
  transition: all 0.3s ease;
}

.preview-content-area.window-dark-mode {
  background: #1f1f1f;
  color: #ffffff;
}
</style>
