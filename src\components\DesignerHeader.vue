<template>
  <div class="designer-header-content">
    
    <!-- 左侧区域：应用Logo和主要操作 -->
    <div class="header-left">
      <!-- 应用Logo和标题 -->
      <div class="app-brand">
        <div class="app-logo">
          <el-icon size="24" color="#409eff"><Monitor /></el-icon>
        </div>
        <div class="app-info">
          <h2 class="app-title">Easy Window</h2>
          <span class="app-subtitle">可视化设计工具</span>
        </div>
      </div>
      
      <!-- 分隔线 -->
      <el-divider direction="vertical" />
      
      <!-- 文件操作按钮组 -->
      <el-button-group size="small">
        <el-dropdown @command="handleNewProject" trigger="click">
          <el-button :icon="DocumentAdd" type="primary" plain>
            新建
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="blank">
                <el-icon><DocumentAdd /></el-icon>
                空白项目
              </el-dropdown-item>
              <el-dropdown-item command="basic">
                <el-icon><Monitor /></el-icon>
                基础窗口模板
              </el-dropdown-item>
              <el-dropdown-item command="form">
                <el-icon><Edit /></el-icon>
                表单应用模板
              </el-dropdown-item>
              <el-dropdown-item command="dashboard">
                <el-icon><DataBoard /></el-icon>
                仪表板模板
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-tooltip content="打开项目 (Ctrl+O)" placement="bottom">
          <el-button :icon="FolderOpened" @click="handleOpen">
            打开
          </el-button>
        </el-tooltip>
        <el-tooltip content="保存项目 (Ctrl+S)" placement="bottom">
          <el-button :icon="Document" @click="handleSave" :disabled="!hasUnsavedChanges">
            保存
          </el-button>
        </el-tooltip>
        <el-tooltip content="另存为 (Ctrl+Shift+S)" placement="bottom">
          <el-button :icon="CopyDocument" @click="handleSaveAs">
            另存为
          </el-button>
        </el-tooltip>
      </el-button-group>
    </div>
    
    <!-- 中间区域：项目信息和状态 -->
    <div class="header-center">
      <div class="project-info">
        <el-tag v-if="currentProjectName" size="small" type="info" effect="plain">
          {{ currentProjectName }}
        </el-tag>
        <el-tag v-if="hasUnsavedChanges" size="small" type="warning" effect="plain">
          未保存
        </el-tag>
        <el-tag v-if="isPreviewMode" size="small" type="success" effect="plain">
          预览模式
        </el-tag>
      </div>
      

    </div>
    
    <!-- 右侧区域：工具和设置 -->
    <div class="header-right">
      
      <!-- 模式切换按钮组 -->
      <el-button-group size="small" style="margin-right: 12px;">
        <el-tooltip :content="isDarkMode ? '切换到浅色模式' : '切换到深色模式'" placement="bottom">
          <el-button 
            :icon="isDarkMode ? Sunny : Moon" 
            @click="toggleDarkMode"
            :type="isDarkMode ? 'success' : 'default'"
          >
            {{ isDarkMode ? '浅色' : '深色' }}
          </el-button>
        </el-tooltip>
        <el-tooltip :content="isPreviewMode ? '退出预览模式' : '进入预览模式 (F5)'" placement="bottom">
          <el-button 
            :icon="View" 
            @click="togglePreview"
            :type="isPreviewMode ? 'success' : 'default'"
          >
            {{ isPreviewMode ? '退出预览' : '预览' }}
          </el-button>
        </el-tooltip>
      </el-button-group>
      
      <!-- 分隔线 -->
      <el-divider direction="vertical" />
      
      <!-- 高级操作按钮组 -->
      <el-button-group size="small" style="margin-right: 12px;">
        <el-tooltip content="项目设置" placement="bottom">
          <el-button :icon="Setting" @click="handleSettings">
            设置
          </el-button>
        </el-tooltip>
        <el-tooltip content="独立预览页面" placement="bottom">
          <el-button :icon="Monitor" @click="openPreviewPage" type="success">
            独立预览
          </el-button>
        </el-tooltip>
        <el-tooltip content="导出代码 (Ctrl+E)" placement="bottom">
          <el-button :icon="Download" @click="handleExport" type="primary">
            导出
          </el-button>
        </el-tooltip>
      </el-button-group>
      
      <!-- 用户菜单 -->
      <el-dropdown @command="handleUserCommand" trigger="click">
        <el-button size="small" circle>
          <el-icon><User /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="help">
              <el-icon><QuestionFilled /></el-icon>
              帮助文档
            </el-dropdown-item>
            <el-dropdown-item command="about">
              <el-icon><InfoFilled /></el-icon>
              关于软件
            </el-dropdown-item>
            <el-dropdown-item divided command="feedback">
              <el-icon><ChatDotRound /></el-icon>
              意见反馈
            </el-dropdown-item>
            <el-dropdown-item command="update">
              <el-icon><Refresh /></el-icon>
              检查更新
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onBeforeUnmount, watch } from 'vue'
import {
  DocumentAdd,
  FolderOpened,
  Document,
  CopyDocument,
  Setting,
  Moon,
  Sunny,
  View,
  Download,
  Monitor,
  User,
  QuestionFilled,
  InfoFilled,
  ChatDotRound,
  Refresh,
  ArrowDown,
  Edit,
  DataBoard
} from '@element-plus/icons-vue'
import { useDark, useToggle } from '@vueuse/core'

// 导入状态管理
import { useDesignerStore } from '@/stores/designer'
import { storeToRefs } from 'pinia'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'

// 获取设计器状态
const designerStore = useDesignerStore()
const { 
  isDarkMode, 
  isPreviewMode, 
  components,
  windows,
  activeWindowId,
  hasUnsavedChanges,
  currentProjectName
} = storeToRefs(designerStore)



// Element Plus 暗黑模式 hook
const isEpDark = useDark({
  selector: 'html',
  valueDark: 'dark',
  valueLight: '',
})
const toggleEpDark = useToggle(isEpDark)

// 键盘快捷键处理
const handleKeyDown = (event: KeyboardEvent) => {
  // 检查是否在输入框中
  if (event.target && ['INPUT', 'TEXTAREA'].includes((event.target as HTMLElement).tagName)) {
    return
  }
  
  const { ctrlKey, shiftKey, key } = event
  
  if (ctrlKey) {
    switch (key.toLowerCase()) {
      case 'n':
        event.preventDefault()
        handleNew()
        break
      case 'o':
        event.preventDefault()
        handleOpen()
        break
      case 's':
        event.preventDefault()
        if (shiftKey) {
          handleSaveAs()
        } else {
          handleSave()
        }
        break


      case 'e':
        event.preventDefault()
        handleExport()
        break
    }
  } else if (key === 'F5') {
    event.preventDefault()
    togglePreview()
  }
}

// 项目模板
const projectTemplates = {
  blank: {
    name: '空白项目',
    windows: [{
      name: '主窗口',
      route: 'home',
      components: []
    }]
  },
  basic: {
    name: '基础窗口模板',
    windows: [{
      name: '主窗口',
      route: 'home',
      components: [
        {
          type: 'text',
          name: '欢迎文本',
          x: 50,
          y: 50,
          width: 200,
          height: 40,
          props: { content: '欢迎使用 Easy Window' }
        },
        {
          type: 'button',
          name: '开始按钮',
          x: 50,
          y: 120,
          width: 100,
          height: 32,
          props: { text: '开始使用', type: 'primary' }
        }
      ]
    }]
  },
  form: {
    name: '表单应用模板',
    windows: [{
      name: '表单页面',
      route: 'form',
      components: [
        {
          type: 'text',
          name: '表单标题',
          x: 50,
          y: 30,
          width: 200,
          height: 40,
          props: { content: '用户信息表单' }
        },
        {
          type: 'input',
          name: '用户名输入',
          x: 50,
          y: 80,
          width: 200,
          height: 32,
          props: { placeholder: '请输入用户名' }
        },
        {
          type: 'input',
          name: '邮箱输入',
          x: 50,
          y: 130,
          width: 200,
          height: 32,
          props: { placeholder: '请输入邮箱' }
        },
        {
          type: 'button',
          name: '提交按钮',
          x: 50,
          y: 180,
          width: 80,
          height: 32,
          props: { text: '提交', type: 'primary' }
        }
      ]
    }]
  },
  dashboard: {
    name: '仪表板模板',
    windows: [{
      name: '仪表板',
      route: 'dashboard',
      components: [
        {
          type: 'text',
          name: '仪表板标题',
          x: 50,
          y: 30,
          width: 200,
          height: 40,
          props: { content: '数据仪表板' }
        },
        {
          type: 'button',
          name: '刷新按钮',
          x: 300,
          y: 30,
          width: 80,
          height: 32,
          props: { text: '刷新', type: 'default' }
        }
      ]
    }]
  }
}

// 新建项目处理函数
const handleNewProject = async (command: string) => {
  if (command === 'blank') {
    await handleNew()
  } else {
    await createFromTemplate(command)
  }
}

// 从模板创建项目
const createFromTemplate = async (templateKey: string) => {
  const template = projectTemplates[templateKey as keyof typeof projectTemplates]
  if (!template) return

  if (hasUnsavedChanges.value) {
    try {
      await ElMessageBox.confirm('当前项目有未保存的更改，是否保存？', '确认', {
        confirmButtonText: '保存',
        cancelButtonText: '不保存',
        distinguishCancelAndClose: true,
        type: 'warning',
      })
      await handleSave()
    } catch (action) {
      if (action === 'cancel') {
        // 用户选择不保存，继续创建
      } else {
        return // 用户取消操作
      }
    }
  }

  try {
    await ElMessageBox.confirm(`创建 "${template.name}" 将清空当前画布，是否继续？`, '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 加载模板数据
    loadProjectData({ windows: template.windows })
    currentProjectName.value = template.name
    designerStore.setHasUnsavedChanges(false)

    ElMessage.success(`已创建 "${template.name}"`)
  } catch {
    // 用户取消操作
  }
}

// 文件操作
const handleNew = async () => {
  if (hasUnsavedChanges.value) {
    try {
      await ElMessageBox.confirm('当前项目有未保存的更改，是否保存？', '确认', {
        confirmButtonText: '保存',
        cancelButtonText: '不保存',
        distinguishCancelAndClose: true,
        type: 'warning',
      })
      await handleSave()
    } catch (action) {
      if (action === 'cancel') {
        // 用户选择不保存，继续新建
      } else {
        return // 用户取消操作
      }
    }
  }
  
  try {
    await ElMessageBox.confirm('新建项目将清空当前画布，是否继续？', '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    designerStore.clearCanvas()
    designerStore.setHasUnsavedChanges(false)
    designerStore.setCurrentProjectName('未命名项目')
    ElMessage.success('已创建新项目')
  } catch {
    // 用户取消操作
  }
}

const handleOpen = () => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json,.ewp'
  input.style.display = 'none'

  input.onchange = async (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file) return

    try {
      const text = await file.text()
      const projectData = JSON.parse(text)

      // 验证项目数据格式
      if (!projectData.windows || !Array.isArray(projectData.windows)) {
        throw new Error('无效的项目文件格式')
      }

      // 检查是否有未保存的更改
      if (hasUnsavedChanges.value) {
        try {
          await ElMessageBox.confirm('当前项目有未保存的更改，是否保存？', '确认', {
            confirmButtonText: '保存',
            cancelButtonText: '不保存',
            distinguishCancelAndClose: true,
            type: 'warning',
          })
          await handleSave()
        } catch (action) {
          if (action !== 'cancel') {
            return // 用户取消操作
          }
        }
      }

      // 加载项目数据
      loadProjectData(projectData)
      currentProjectName.value = projectData.name || file.name.replace(/\.(json|ewp)$/, '')
      designerStore.setHasUnsavedChanges(false)

      ElMessage.success(`项目 "${currentProjectName.value}" 加载成功`)
    } catch (error) {
      console.error('加载项目失败:', error)
      ElMessage.error('加载项目失败：' + (error as Error).message)
    }

    // 清理
    document.body.removeChild(input)
  }

  document.body.appendChild(input)
  input.click()
}

const handleSave = async () => {
  try {
    // 保存到本地存储
    const projectData = exportProjectData()
    localStorage.setItem('easy-window-current-project', JSON.stringify(projectData))
    localStorage.setItem('easy-window-project-name', currentProjectName.value || '未命名项目')

    designerStore.setHasUnsavedChanges(false)
    ElMessage.success('项目已保存到本地')
  } catch (error) {
    console.error('保存项目失败:', error)
    ElMessage.error('保存项目失败')
  }
}

const handleSaveAs = async () => {
  try {
    // 弹出项目名称输入框
    const { value: projectName } = await ElMessageBox.prompt('请输入项目名称', '另存为', {
      confirmButtonText: '保存',
      cancelButtonText: '取消',
      inputValue: currentProjectName.value || '未命名项目',
      inputPattern: /^[^<>:"/\\|?*]+$/,
      inputErrorMessage: '项目名称不能包含特殊字符'
    })

    if (!projectName) return

    // 导出项目数据
    const projectData = exportProjectData()
    projectData.name = projectName

    // 创建下载链接
    const blob = new Blob([JSON.stringify(projectData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${projectName}.ewp`
    link.style.display = 'none'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    // 更新当前项目名称
    currentProjectName.value = projectName
    designerStore.setHasUnsavedChanges(false)

    ElMessage.success(`项目 "${projectName}" 已导出`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('另存为失败:', error)
      ElMessage.error('另存为失败')
    }
  }
}





// 项目数据操作函数
const exportProjectData = () => {
  return {
    name: currentProjectName.value || '未命名项目',
    version: '1.0.0',
    createdAt: new Date().toISOString(),
    windows: windows.value.map(window => ({
      ...window,
      components: window.components.map(comp => ({ ...comp }))
    })),
    settings: {
      darkMode: isDarkMode.value,
      lastActiveWindow: activeWindowId.value
    }
  }
}

const loadProjectData = (projectData: any) => {
  try {
    console.log('开始加载项目数据:', projectData)

    // 清空当前画布
    designerStore.clearCanvas()

    // 清空所有窗口
    const currentWindows = [...windows.value]
    currentWindows.forEach(window => {
      designerStore.removeWindow(window.id)
    })

    // 加载窗口数据
    if (projectData.windows && Array.isArray(projectData.windows)) {
      projectData.windows.forEach((windowData: any, index: number) => {
        console.log(`加载窗口 ${index + 1}:`, windowData)

        // 确保窗口数据完整性
        const completeWindowData = {
          id: windowData.id || `window-${Date.now()}-${index}`,
          name: windowData.name || `窗口${index + 1}`,
          route: windowData.route || `window${index + 1}`,
          windowType: windowData.windowType || 'normal',
          components: windowData.components || [],
          selectedComponentId: windowData.selectedComponentId || null,
          canvasConfig: {
            width: 1200,
            height: 800,
            backgroundColor: '#ffffff',
            gridSize: 10,
            showGrid: true,
            ...windowData.canvasConfig
          },
          windowConfig: {
            width: 800,
            height: 600,
            backgroundColor: '#fff',
            border: '1.5px solid #bfcbd9',
            borderRadius: 6,
            boxShadow: '0 8px 32px rgba(0,0,0,0.18)',
            ...windowData.windowConfig
          },
          titleBarConfig: {
            title: windowData.name || `窗口${index + 1}`,
            showLogo: true,
            logoPath: '',
            showDarkToggle: true,
            showMinimize: true,
            showMaximize: true,
            showClose: true,
            isDarkMode: false,
            titleColor: '',
            backgroundColor: '',
            height: 36,
            ...windowData.titleBarConfig
          }
        }

        // 添加窗口（这会自动设置为活动窗口）
        const windowId = designerStore.addWindow(completeWindowData)

        // 如果有组件数据，添加组件
        if (completeWindowData.components && completeWindowData.components.length > 0) {
          console.log(`为窗口 ${completeWindowData.name} 加载 ${completeWindowData.components.length} 个组件`)

          // 切换到当前窗口
          designerStore.setActiveWindow(windowId)

          // 清空当前组件（因为addWindow可能有默认组件）
          designerStore.clearCanvas()

          // 添加组件
          completeWindowData.components.forEach((componentData: any) => {
            console.log('添加组件:', componentData)
            designerStore.addComponent({
              type: componentData.type,
              name: componentData.name,
              x: componentData.x,
              y: componentData.y,
              width: componentData.width,
              height: componentData.height,
              props: componentData.props || {},
              style: componentData.style || {}
            })
          })

          // 恢复选中状态
          if (completeWindowData.selectedComponentId) {
            designerStore.setSelectedComponent(completeWindowData.selectedComponentId)
          }
        }
      })
    }

    // 恢复设置
    if (projectData.settings) {
      console.log('恢复项目设置:', projectData.settings)

      if (typeof projectData.settings.darkMode === 'boolean' && projectData.settings.darkMode !== isDarkMode.value) {
        designerStore.toggleDarkMode()
      }

      if (projectData.settings.lastActiveWindow && windows.value.find(w => w.id === projectData.settings.lastActiveWindow)) {
        designerStore.setActiveWindow(projectData.settings.lastActiveWindow)
      }
    }

    // 如果没有窗口，创建默认窗口
    if (windows.value.length === 0) {
      console.log('没有窗口数据，创建默认窗口')
      designerStore.addWindow({
        name: '主窗口',
        route: 'home'
      })
    }

    console.log('项目数据加载完成，当前窗口数量:', windows.value.length)
    console.log('当前活动窗口:', activeWindowId.value)

  } catch (error) {
    console.error('加载项目数据失败:', error)
    throw new Error('项目数据格式错误: ' + (error as Error).message)
  }
}

// 自动保存功能
const autoSave = () => {
  if (hasUnsavedChanges.value && currentProjectName.value) {
    const projectData = exportProjectData()
    localStorage.setItem('easy-window-auto-save', JSON.stringify(projectData))
    localStorage.setItem('easy-window-auto-save-time', new Date().toISOString())
  }
}

// 恢复项目功能
const restoreProject = () => {
  try {
    const savedProject = localStorage.getItem('easy-window-current-project')
    const savedName = localStorage.getItem('easy-window-project-name')

    if (savedProject) {
      const projectData = JSON.parse(savedProject)
      loadProjectData(projectData)
      currentProjectName.value = savedName || '未命名项目'
      designerStore.setHasUnsavedChanges(false)
      ElMessage.success('已恢复上次保存的项目')
    }
  } catch (error) {
    console.error('恢复项目失败:', error)
    ElMessage.warning('恢复项目失败，将创建新项目')
  }
}

// 检查自动保存
const checkAutoSave = () => {
  const autoSaveData = localStorage.getItem('easy-window-auto-save')
  const autoSaveTime = localStorage.getItem('easy-window-auto-save-time')

  if (autoSaveData && autoSaveTime) {
    const saveTime = new Date(autoSaveTime)
    const now = new Date()
    const diffMinutes = (now.getTime() - saveTime.getTime()) / (1000 * 60)

    // 如果自动保存时间在30分钟内，提示恢复
    if (diffMinutes < 30) {
      ElMessageBox.confirm(
        `检测到 ${Math.round(diffMinutes)} 分钟前的自动保存，是否恢复？`,
        '恢复项目',
        {
          confirmButtonText: '恢复',
          cancelButtonText: '忽略',
          type: 'info'
        }
      ).then(() => {
        try {
          const projectData = JSON.parse(autoSaveData)
          loadProjectData(projectData)
          currentProjectName.value = '自动保存恢复'
          designerStore.setHasUnsavedChanges(true)
          ElMessage.success('已恢复自动保存的项目')
        } catch (error) {
          ElMessage.error('恢复自动保存失败')
        }
      }).catch(() => {
        // 用户选择忽略，清除自动保存
        localStorage.removeItem('easy-window-auto-save')
        localStorage.removeItem('easy-window-auto-save-time')
      })
    }
  }
}

// 设置和导出
const handleSettings = () => {
  ElMessage.info('设置功能开发中...')
  // TODO: 实现设置面板
}

const handleExport = async () => {
  try {
    // 如果项目有未保存的更改，先保存
    if (hasUnsavedChanges.value) {
      await handleSave()
    }

    // 获取项目名称
    let projectName = currentProjectName.value || 'easy-window-project'

    // 如果没有项目名称，提示用户输入
    if (!currentProjectName.value) {
      const { value } = await ElMessageBox.prompt('请输入项目名称', '导出项目', {
        confirmButtonText: '导出',
        cancelButtonText: '取消',
        inputValue: 'easy-window-project',
        inputPattern: /^[^<>:"/\\|?*]+$/,
        inputErrorMessage: '项目名称不能包含特殊字符'
      })

      if (value) {
        projectName = value
        currentProjectName.value = value
      } else {
        return // 用户取消
      }
    }

    const loading = ElLoading.service({
      lock: true,
      text: `正在导出项目 "${projectName}"...`,
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      // 导入导出工具
      const { exportProject } = await import('@/utils/exportProject')

      // 获取项目数据
      const windowsData = windows.value

      if (windowsData.length === 0) {
        ElMessage.warning('没有窗口数据可导出')
        return
      }

      // 执行导出
      await exportProject(windowsData, projectName)

      ElMessage.success(`项目 "${projectName}" 导出成功！`)
    } finally {
      loading.close()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('导出失败:', error)
      ElMessage.error('导出失败，请检查控制台错误信息')
    }
  }
}

// 打开独立预览页面
const openPreviewPage = () => {
  try {
    // 将设计器数据保存到localStorage，供预览页面使用
    const designerData = {
      windows: windows.value,
      activeWindowId: activeWindowId.value,
      timestamp: Date.now()
    }
    localStorage.setItem('designer-preview-data', JSON.stringify(designerData))

    // 在新窗口中打开预览页面
    window.open('/preview', '_blank')
    ElMessage.success('已在新窗口中打开预览页面')
  } catch (error) {
    console.error('保存预览数据失败:', error)
    ElMessage.error('打开预览页面失败')
  }
}

// 模式切换
const toggleDarkMode = () => {
  toggleEpDark()
  designerStore.toggleDarkMode()
  ElMessage.success(`已切换到${isEpDark.value ? '深色' : '浅色'}模式`)
}

const togglePreview = () => {
  designerStore.togglePreviewMode()
  ElMessage.success(`已${isPreviewMode.value ? '进入' : '退出'}预览模式`)
}

// 用户菜单命令处理
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'help':
      ElMessage.info('帮助文档功能开发中...')
      break
    case 'about':
      ElMessage.info('关于软件功能开发中...')
      break
    case 'feedback':
      ElMessage.info('意见反馈功能开发中...')
      break
    case 'update':
      ElMessage.info('检查更新功能开发中...')
      break
  }
}

// 功能验证函数
const validateFunctionality = () => {
  console.log('=== 功能验证开始 ===')

  // 1. 验证保存按钮状态
  console.log('1. 保存按钮状态:', hasUnsavedChanges.value ? '启用' : '禁用')

  // 2. 验证项目数据导出
  try {
    const projectData = exportProjectData()
    console.log('2. 项目数据导出:', projectData ? '成功' : '失败')
    console.log('   - 窗口数量:', projectData.windows?.length || 0)
    console.log('   - 项目名称:', projectData.name)
  } catch (error) {
    console.error('2. 项目数据导出失败:', error)
  }

  // 3. 验证本地存储
  try {
    const saved = localStorage.getItem('easy-window-current-project')
    console.log('3. 本地存储:', saved ? '有数据' : '无数据')
  } catch (error) {
    console.error('3. 本地存储检查失败:', error)
  }

  // 4. 验证窗口状态
  console.log('4. 当前窗口状态:')
  console.log('   - 窗口数量:', windows.value.length)
  console.log('   - 活动窗口:', activeWindowId.value)
  console.log('   - 暗黑模式:', isDarkMode.value)

  console.log('=== 功能验证完成 ===')
}

// 状态监控
watch(hasUnsavedChanges, (newValue, oldValue) => {
  console.log('未保存状态变化:', oldValue, '->', newValue)
}, { immediate: true })

watch(windows, (newWindows) => {
  console.log('窗口数据变化:', newWindows.length, '个窗口')
}, { deep: true })

watch(activeWindowId, (newId, oldId) => {
  console.log('活动窗口切换:', oldId, '->', newId)
})

// 生命周期
onMounted(() => {
  window.addEventListener('keydown', handleKeyDown)

  // 检查自动保存
  checkAutoSave()

  // 尝试恢复上次项目
  if (!currentProjectName.value) {
    restoreProject()
  }

  // 设置自动保存定时器（每5分钟）
  const autoSaveInterval = setInterval(autoSave, 5 * 60 * 1000)

  // 页面卸载前保存
  const handleBeforeUnload = () => {
    autoSave()
  }

  window.addEventListener('beforeunload', handleBeforeUnload)

  // 开发模式下进行功能验证
  if (import.meta.env.DEV) {
    setTimeout(validateFunctionality, 1000)
  }

  // 清理函数
  onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown)
    window.removeEventListener('beforeunload', handleBeforeUnload)
    clearInterval(autoSaveInterval)
  })
})

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
/* 头部内容容器 */
.designer-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 0 20px;
  background: linear-gradient(135deg, var(--el-bg-color) 0%, var(--el-bg-color-overlay) 100%);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 左侧区域 */
.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

/* 应用品牌区域 */
.app-brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border-radius: 8px;
  color: white;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.app-info {
  display: flex;
  flex-direction: column;
}

.app-title {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: var(--el-color-primary);
  line-height: 1.2;
}

.app-subtitle {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 2px;
}

/* 中间区域 */
.header-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
  max-width: 400px;
}

.project-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

.window-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 右侧区域 */
.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* 按钮组样式调整 */
.el-button-group .el-button {
  margin: 0;
}

/* 分隔线样式 */
.el-divider--vertical {
  height: 24px;
  margin: 0 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-center {
    max-width: 300px;
  }
  
  .app-subtitle {
    display: none;
  }
}

@media (max-width: 900px) {
  .header-center {
    display: none;
  }
  
  .app-info {
    display: none;
  }
}

/* 悬停效果 */
.app-logo:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* 按钮悬停效果 */
.el-button:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

/* 标签样式优化 */
.el-tag {
  border-radius: 6px;
  font-weight: 500;
}

/* 工具提示样式 */
:deep(.el-tooltip__trigger) {
  display: inline-block;
}
</style>