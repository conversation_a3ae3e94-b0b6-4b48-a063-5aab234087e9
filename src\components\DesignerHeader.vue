<template>
  <div class="designer-header-content">
    
    <!-- 左侧区域：应用Logo和主要操作 -->
    <div class="header-left">
      <!-- 应用Logo和标题 -->
      <div class="app-brand">
        <div class="app-logo">
          <el-icon size="24" color="#409eff"><Monitor /></el-icon>
        </div>
        <div class="app-info">
          <h2 class="app-title">Easy Window</h2>
          <span class="app-subtitle">可视化设计工具</span>
        </div>
      </div>
      
      <!-- 分隔线 -->
      <el-divider direction="vertical" />
      
      <!-- 文件操作按钮组 -->
      <el-button-group size="small">
        <el-tooltip content="新建项目 (Ctrl+N)" placement="bottom">
          <el-button :icon="DocumentAdd" @click="handleNew" type="primary" plain>
            新建
          </el-button>
        </el-tooltip>
        <el-tooltip content="打开项目 (Ctrl+O)" placement="bottom">
          <el-button :icon="FolderOpened" @click="handleOpen">
            打开
          </el-button>
        </el-tooltip>
        <el-tooltip content="保存项目 (Ctrl+S)" placement="bottom">
          <el-button :icon="Document" @click="handleSave" :disabled="!hasUnsavedChanges">
            保存
          </el-button>
        </el-tooltip>
        <el-tooltip content="另存为 (Ctrl+Shift+S)" placement="bottom">
          <el-button :icon="CopyDocument" @click="handleSaveAs">
            另存为
          </el-button>
        </el-tooltip>
      </el-button-group>
    </div>
    
    <!-- 中间区域：项目信息和状态 -->
    <div class="header-center">
      <div class="project-info">
        <el-tag v-if="currentProjectName" size="small" type="info" effect="plain">
          {{ currentProjectName }}
        </el-tag>
        <el-tag v-if="hasUnsavedChanges" size="small" type="warning" effect="plain">
          未保存
        </el-tag>
        <el-tag v-if="isPreviewMode" size="small" type="success" effect="plain">
          预览模式
        </el-tag>
      </div>
      

    </div>
    
    <!-- 右侧区域：工具和设置 -->
    <div class="header-right">
      
      <!-- 模式切换按钮组 -->
      <el-button-group size="small" style="margin-right: 12px;">
        <el-tooltip :content="isDarkMode ? '切换到浅色模式' : '切换到深色模式'" placement="bottom">
          <el-button 
            :icon="isDarkMode ? Sunny : Moon" 
            @click="toggleDarkMode"
            :type="isDarkMode ? 'success' : 'default'"
          >
            {{ isDarkMode ? '浅色' : '深色' }}
          </el-button>
        </el-tooltip>
        <el-tooltip :content="isPreviewMode ? '退出预览模式' : '进入预览模式 (F5)'" placement="bottom">
          <el-button 
            :icon="View" 
            @click="togglePreview"
            :type="isPreviewMode ? 'success' : 'default'"
          >
            {{ isPreviewMode ? '退出预览' : '预览' }}
          </el-button>
        </el-tooltip>
      </el-button-group>
      
      <!-- 分隔线 -->
      <el-divider direction="vertical" />
      
      <!-- 高级操作按钮组 -->
      <el-button-group size="small" style="margin-right: 12px;">
        <el-tooltip content="项目设置" placement="bottom">
          <el-button :icon="Setting" @click="handleSettings">
            设置
          </el-button>
        </el-tooltip>
        <el-tooltip content="独立预览页面" placement="bottom">
          <el-button :icon="Monitor" @click="openPreviewPage" type="success">
            独立预览
          </el-button>
        </el-tooltip>
        <el-tooltip content="导出代码 (Ctrl+E)" placement="bottom">
          <el-button :icon="Download" @click="handleExport" type="primary">
            导出
          </el-button>
        </el-tooltip>
      </el-button-group>
      
      <!-- 用户菜单 -->
      <el-dropdown @command="handleUserCommand" trigger="click">
        <el-button size="small" circle>
          <el-icon><User /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="help">
              <el-icon><QuestionFilled /></el-icon>
              帮助文档
            </el-dropdown-item>
            <el-dropdown-item command="about">
              <el-icon><InfoFilled /></el-icon>
              关于软件
            </el-dropdown-item>
            <el-dropdown-item divided command="feedback">
              <el-icon><ChatDotRound /></el-icon>
              意见反馈
            </el-dropdown-item>
            <el-dropdown-item command="update">
              <el-icon><Refresh /></el-icon>
              检查更新
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onBeforeUnmount } from 'vue'
import { 
  DocumentAdd, 
  FolderOpened, 
  Document, 
  CopyDocument, 
  Setting,
  Moon,
  Sunny,
  View,
  Download,
  Monitor,
  User,
  QuestionFilled,
  InfoFilled,
  ChatDotRound,
  Refresh
} from '@element-plus/icons-vue'
import { useDark, useToggle } from '@vueuse/core'

// 导入状态管理
import { useDesignerStore } from '@/stores/designer'
import { storeToRefs } from 'pinia'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'

// 获取设计器状态
const designerStore = useDesignerStore()
const { 
  isDarkMode, 
  isPreviewMode, 
  components,
  windows,
  activeWindowId,
  hasUnsavedChanges,
  currentProjectName
} = storeToRefs(designerStore)



// Element Plus 暗黑模式 hook
const isEpDark = useDark({
  selector: 'html',
  valueDark: 'dark',
  valueLight: '',
})
const toggleEpDark = useToggle(isEpDark)

// 键盘快捷键处理
const handleKeyDown = (event: KeyboardEvent) => {
  // 检查是否在输入框中
  if (event.target && ['INPUT', 'TEXTAREA'].includes((event.target as HTMLElement).tagName)) {
    return
  }
  
  const { ctrlKey, shiftKey, key } = event
  
  if (ctrlKey) {
    switch (key.toLowerCase()) {
      case 'n':
        event.preventDefault()
        handleNew()
        break
      case 'o':
        event.preventDefault()
        handleOpen()
        break
      case 's':
        event.preventDefault()
        if (shiftKey) {
          handleSaveAs()
        } else {
          handleSave()
        }
        break


      case 'e':
        event.preventDefault()
        handleExport()
        break
    }
  } else if (key === 'F5') {
    event.preventDefault()
    togglePreview()
  }
}

// 文件操作
const handleNew = async () => {
  if (hasUnsavedChanges.value) {
    try {
      await ElMessageBox.confirm('当前项目有未保存的更改，是否保存？', '确认', {
        confirmButtonText: '保存',
        cancelButtonText: '不保存',
        distinguishCancelAndClose: true,
        type: 'warning',
      })
      await handleSave()
    } catch (action) {
      if (action === 'cancel') {
        // 用户选择不保存，继续新建
      } else {
        return // 用户取消操作
      }
    }
  }
  
  try {
    await ElMessageBox.confirm('新建项目将清空当前画布，是否继续？', '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    designerStore.clearCanvas()
    designerStore.setHasUnsavedChanges(false)
    designerStore.setCurrentProjectName('未命名项目')
    ElMessage.success('已创建新项目')
  } catch {
    // 用户取消操作
  }
}

const handleOpen = () => {
  ElMessage.info('打开功能开发中...')
  // TODO: 实现文件选择和项目加载功能
}

const handleSave = () => {
  ElMessage.success('项目已保存')
  designerStore.setHasUnsavedChanges(false)
  // TODO: 实现项目保存功能
}

const handleSaveAs = () => {
  ElMessage.info('另存为功能开发中...')
  // TODO: 实现另存为功能
}





// 设置和导出
const handleSettings = () => {
  ElMessage.info('设置功能开发中...')
  // TODO: 实现设置面板
}

const handleExport = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '正在生成项目文件...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    // 导入导出工具
    const { exportProject } = await import('@/utils/exportProject')

    // 获取项目数据
    const projectName = currentProjectName.value || 'easy-window-project'
    const windowsData = windows.value

    if (windowsData.length === 0) {
      ElMessage.warning('没有窗口数据可导出')
      return
    }

    // 执行导出
    await exportProject(windowsData, projectName)

    ElMessage.success('项目导出成功！')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请检查控制台错误信息')
  } finally {
    loading.close()
  }
}

// 打开独立预览页面
const openPreviewPage = () => {
  try {
    // 将设计器数据保存到localStorage，供预览页面使用
    const designerData = {
      windows: windows.value,
      activeWindowId: activeWindowId.value,
      timestamp: Date.now()
    }
    localStorage.setItem('designer-preview-data', JSON.stringify(designerData))

    // 在新窗口中打开预览页面
    window.open('/preview', '_blank')
    ElMessage.success('已在新窗口中打开预览页面')
  } catch (error) {
    console.error('保存预览数据失败:', error)
    ElMessage.error('打开预览页面失败')
  }
}

// 模式切换
const toggleDarkMode = () => {
  toggleEpDark()
  designerStore.toggleDarkMode()
  ElMessage.success(`已切换到${isEpDark.value ? '深色' : '浅色'}模式`)
}

const togglePreview = () => {
  designerStore.togglePreviewMode()
  ElMessage.success(`已${isPreviewMode.value ? '进入' : '退出'}预览模式`)
}

// 用户菜单命令处理
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'help':
      ElMessage.info('帮助文档功能开发中...')
      break
    case 'about':
      ElMessage.info('关于软件功能开发中...')
      break
    case 'feedback':
      ElMessage.info('意见反馈功能开发中...')
      break
    case 'update':
      ElMessage.info('检查更新功能开发中...')
      break
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('keydown', handleKeyDown)
})

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
/* 头部内容容器 */
.designer-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 0 20px;
  background: linear-gradient(135deg, var(--el-bg-color) 0%, var(--el-bg-color-overlay) 100%);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 左侧区域 */
.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

/* 应用品牌区域 */
.app-brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border-radius: 8px;
  color: white;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.app-info {
  display: flex;
  flex-direction: column;
}

.app-title {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: var(--el-color-primary);
  line-height: 1.2;
}

.app-subtitle {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 2px;
}

/* 中间区域 */
.header-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
  max-width: 400px;
}

.project-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

.window-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 右侧区域 */
.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* 按钮组样式调整 */
.el-button-group .el-button {
  margin: 0;
}

/* 分隔线样式 */
.el-divider--vertical {
  height: 24px;
  margin: 0 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-center {
    max-width: 300px;
  }
  
  .app-subtitle {
    display: none;
  }
}

@media (max-width: 900px) {
  .header-center {
    display: none;
  }
  
  .app-info {
    display: none;
  }
}

/* 悬停效果 */
.app-logo:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* 按钮悬停效果 */
.el-button:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

/* 标签样式优化 */
.el-tag {
  border-radius: 6px;
  font-weight: 500;
}

/* 工具提示样式 */
:deep(.el-tooltip__trigger) {
  display: inline-block;
}
</style>